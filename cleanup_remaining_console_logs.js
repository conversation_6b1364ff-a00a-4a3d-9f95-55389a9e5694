#!/usr/bin/env node

/**
 * <PERSON>ript to identify remaining console.log statements in the codebase
 * and provide a summary of what needs to be cleaned up
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Function to find all console statements
function findConsoleStatements() {
  try {
    // Use grep to find all console statements
    const result = execSync('grep -r "console\\.(log|warn|error|info|debug)" src/ --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx"', { encoding: 'utf8' });
    
    const lines = result.split('\n').filter(line => line.trim());
    const consoleStats = {
      log: 0,
      warn: 0,
      error: 0,
      info: 0,
      debug: 0,
      total: 0
    };
    
    const filesWithConsole = new Set();
    
    lines.forEach(line => {
      if (line.includes('console.log')) consoleStats.log++;
      if (line.includes('console.warn')) consoleStats.warn++;
      if (line.includes('console.error')) consoleStats.error++;
      if (line.includes('console.info')) consoleStats.info++;
      if (line.includes('console.debug')) consoleStats.debug++;
      consoleStats.total++;
      
      // Extract filename
      const match = line.match(/^([^:]+):/);
      if (match) {
        filesWithConsole.add(match[1]);
      }
    });
    
    return {
      stats: consoleStats,
      files: Array.from(filesWithConsole),
      lines: lines
    };
  } catch (error) {
    console.error('Error finding console statements:', error.message);
    return { stats: {}, files: [], lines: [] };
  }
}

// Function to generate cleanup report
function generateCleanupReport(data) {
  console.log('\n🔍 CONSOLE LOG CLEANUP REPORT');
  console.log('================================\n');
  
  console.log('📊 CONSOLE STATEMENT STATISTICS:');
  console.log(`   console.log: ${data.stats.log || 0}`);
  console.log(`   console.warn: ${data.stats.warn || 0}`);
  console.log(`   console.error: ${data.stats.error || 0}`);
  console.log(`   console.info: ${data.stats.info || 0}`);
  console.log(`   console.debug: ${data.stats.debug || 0}`);
  console.log(`   TOTAL: ${data.stats.total || 0}\n`);
  
  console.log('📁 FILES WITH CONSOLE STATEMENTS:');
  data.files.forEach(file => {
    console.log(`   ${file}`);
  });
  
  console.log('\n🎯 PRIORITY CLEANUP TARGETS:');
  console.log('   High Priority (Core files):');
  console.log('   - src/lib/ (Database, JWT, Email, SMS services)');
  console.log('   - src/hooks/ (Custom hooks)');
  console.log('   - src/components/ (React components)');
  console.log('   - src/app/ (Page components)');
  
  console.log('\n💡 RECOMMENDED APPROACH:');
  console.log('   1. Start with lib/ files (core utilities)');
  console.log('   2. Clean up hooks/ files');
  console.log('   3. Work on components/ files');
  console.log('   4. Finish with app/ page files');
  console.log('   5. Use logger.debug() for development-only logs');
  console.log('   6. Use logger.error() for error logs');
  console.log('   7. Use logger.warn() for warning logs');
  
  console.log('\n⚠️  IMPORTANT NOTES:');
  console.log('   - Keep console.error in logger.ts itself (it\'s the logger)');
  console.log('   - Some console statements might be intentional for debugging');
  console.log('   - Test thoroughly after each file cleanup');
  console.log('   - Consider environment-specific logging');
}

// Main execution
if (require.main === module) {
  const data = findConsoleStatements();
  generateCleanupReport(data);
  
  console.log('\n🚀 READY TO CLEAN UP!');
  console.log('Use the logger utility we created to replace console statements.');
  console.log('Example: console.log("debug info") → logger.debug("debug info")');
  console.log('Example: console.error("error") → logger.error("error")');
} 