// Production-safe logging utility
type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableServerLogs: boolean;
}

const LOG_LEVELS: Record<LogLevel, number> = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
};

class Logger {
  private config: LoggerConfig;

  constructor() {
    // In production, only show warnings and errors
    // In development, show all logs
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    this.config = {
      level: isDevelopment ? 'debug' : 'warn',
      enableConsole: isDevelopment,
      enableServerLogs: true, // Always enable server-side logging for errors
    };
  }

  private shouldLog(level: LogLevel): boolean {
    return LOG_LEVELS[level] >= LOG_LEVELS[this.config.level];
  }

  private formatMessage(level: LogLevel, message: string, ...args: any[]): string {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}]`;
    
    if (args.length > 0) {
      return `${prefix} ${message} ${args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ')}`;
    }
    
    return `${prefix} ${message}`;
  }

  debug(message: string, ...args: any[]): void {
    if (this.shouldLog('debug') && this.config.enableConsole) {
      console.log(this.formatMessage('debug', message, ...args));
    }
  }

  info(message: string, ...args: any[]): void {
    if (this.shouldLog('info') && this.config.enableConsole) {
      console.log(this.formatMessage('info', message, ...args));
    }
  }

  warn(message: string, ...args: any[]): void {
    if (this.shouldLog('warn') && this.config.enableConsole) {
      console.warn(this.formatMessage('warn', message, ...args));
    }
  }

  error(message: string, ...args: any[]): void {
    if (this.shouldLog('error')) {
      // Always log errors to console in development, and to server logs in production
      if (this.config.enableConsole) {
        console.error(this.formatMessage('error', message, ...args));
      }
      
      // In production, you might want to send errors to a logging service
      if (this.config.enableServerLogs && process.env.NODE_ENV === 'production') {
        // TODO: Implement server-side error logging (e.g., to a logging service)
        // For now, we'll just use console.error in production for critical errors
        console.error(this.formatMessage('error', message, ...args));
      }
    }
  }

  // Method to log errors that should always be logged (critical errors)
  critical(message: string, ...args: any[]): void {
    console.error(this.formatMessage('error', `[CRITICAL] ${message}`, ...args));
  }
}

// Create a singleton instance
const logger = new Logger();

export default logger;

// Export individual methods for convenience
export const { debug, info, warn, error, critical } = logger; 