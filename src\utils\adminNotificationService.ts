import { query } from '@/lib/db';
import { sendEmail } from '@/lib/consolidatedEmailService';
import { getServerAppUrl } from '@/utils/appUrl';
import logger from '@/utils/logger';

interface AdminNotificationParams {
  type: string;
  title: string;
  message: string;
  entityType?: string | null;
  entityId?: number | null;
  shouldSendEmail?: boolean;
  emailSubject?: string;
}

/**
 * Create a new notification for admin users
 */
export async function createAdminNotification({
  type,
  title,
  message,
  entityType = null,
  entityId = null,
  shouldSendEmail = true,
  emailSubject
}: AdminNotificationParams): Promise<{ success: boolean; notificationId?: number; error?: string }> {
  try {
    logger.debug('Creating admin notification:', { type, title, message, entityType, entityId });

    // Ensure the admin_notifications table exists
    await ensureAdminNotificationsTable();
    logger.debug('Admin notifications table ensured');

    // Determine link based on notification type
    let link = null;

    if (type === 'new_cremation_center' || type === 'pending_application') {
      // Link to the applications page
      link = '/admin/applications';

      // If we have a specific entity ID, link directly to that application
      if (entityId) {
        link = `/admin/applications/${entityId}`;
      }
    } else if (type === 'refund_request') {
      // Link to the refunds page
      link = '/admin/refunds';

      // If we have a specific entity ID, link directly to that refund
      if (entityId) {
        link = `/admin/refunds?refundId=${entityId}`;
      }
    } else if (type === 'new_appeal' || type === 'appeal_submitted') {
      // Link to the appropriate admin users page based on entity type
      if (entityType === 'furparent' || entityType === 'user') {
        link = '/admin/users/furparents';
      } else if (entityType === 'cremation' || entityType === 'business') {
        link = '/admin/users/cremation';
      } else {
        // Default to furparents if type is unclear
        link = '/admin/users/furparents';
      }

      // If we have a specific entity ID (user ID), add it as a parameter
      if (entityId) {
        link += `?appealId=${entityId}&userId=${entityId}`;
      }
    }

    // Insert the notification
    logger.debug('Inserting admin notification with data:', { type, title, message, entityType, entityId, link });
    const result = await query(
      `INSERT INTO admin_notifications (type, title, message, entity_type, entity_id, link)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [type, title, message, entityType, entityId, link]
    ) as any;
    logger.debug('Admin notification inserted with ID:', result.insertId);

    // Send email notifications to all admins if requested
    if (shouldSendEmail) {
      logger.debug('Sending admin email notifications...');
      await sendAdminEmailNotifications(title, message, type, link, emailSubject);
    }

    return {
      success: true,
      notificationId: result.insertId
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error creating notification'
    };
  }
}

/**
 * Send email notifications to all admins with email notifications enabled
 */
async function sendAdminEmailNotifications(
  title: string,
  message: string,
  type: string,
  link: string | null,
  emailSubject?: string
): Promise<void> {
  try {
    // Get all admins with email notifications enabled
    const adminsResult = await query(`
      SELECT user_id, email, first_name, email_notifications
      FROM users 
      WHERE role = 'admin' 
      AND (email_notifications IS NULL OR email_notifications = 1)
      AND email IS NOT NULL
    `) as any[];

    if (!adminsResult || adminsResult.length === 0) {
      return;
    }

    // Send email to each admin
    const emailPromises = adminsResult.map(async (admin) => {
      try {
        await sendEmail({
          to: admin.email,
          subject: emailSubject || `[Rainbow Paws Admin] ${title}`,
          html: createAdminEmailHtml(admin.first_name, title, message, type, link),
          text: createAdminEmailText(admin.first_name, title, message, link)
        });
      } catch (emailError) {
        logger.error(`Failed to send admin email to ${admin.email}:`, emailError);
      }
    });

    await Promise.allSettled(emailPromises);
  } catch (error) {
    logger.error('Error sending admin email notifications:', error);
  }
}

/**
 * Create HTML email content for admin notification using the Rainbow Paws base template
 */
function createAdminEmailHtml(firstName: string, title: string, message: string, type: string, link: string | null): string {
  // Use the same base template as the main email templates
  const baseEmailTemplate = (content: string) => `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>RainbowPaws Notification</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #10B981;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      color: white;
      margin: 0;
      font-size: 24px;
    }
    .content {
      padding: 20px;
      background-color: #fff;
    }
    .footer {
      background-color: #f5f5f5;
      padding: 15px;
      text-align: center;
      font-size: 12px;
      color: #666;
    }
    .button {
      display: inline-block;
      background-color: #10B981;
      color: white;
      padding: 12px 24px;
      text-decoration: none;
      border-radius: 25px;
      margin: 20px 0;
      font-weight: normal;
    }
    .info-box {
      background-color: #f9f9f9;
      border: 1px solid #eee;
      border-radius: 4px;
      padding: 15px;
      margin: 15px 0;
    }
    .admin-badge {
      background-color: #dc2626;
      color: white;
      padding: 6px 12px;
      border-radius: 15px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      display: inline-block;
      margin-bottom: 15px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>RainbowPaws</h1>
    </div>
    <div class="content">
      ${content}
    </div>
    <div class="footer">
      <p>&copy; ${new Date().getFullYear()} RainbowPaws - Pet Memorial Services</p>
      <p>This is an automated message, please do not reply to this email.</p>
    </div>
  </div>
</body>
</html>
`;

  // Button text based on notification type
  const getButtonText = () => {
    if (type === 'new_cremation_center' || type === 'pending_application') return 'Review Application';
    if (type === 'refund_request') return 'Review Refund';
    return 'View Details';
  };

  const appUrl = getServerAppUrl();

  const content = `
    <h2>Admin Notification</h2>
    <p>Hello ${firstName},</p>
    <span class="admin-badge">Admin Alert</span>
    <h3>${title}</h3>
    <p>${message}</p>
    ${link ? `<div style="text-align: center;"><a href="${appUrl}${link}" class="button">${getButtonText()}</a></div>` : ''}
    <div class="info-box">
      <p><strong>Note:</strong> This notification requires your attention as an administrator.</p>
    </div>
  `;

  return baseEmailTemplate(content);
}

/**
 * Create plain text email content for admin notification
 */
function createAdminEmailText(firstName: string, title: string, message: string, link: string | null): string {
  const appUrl = getServerAppUrl();

  return `
Rainbow Paws Admin Notification

Hello ${firstName},

${title}

${message}

${link ? `Admin Panel Link: ${appUrl}${link}` : ''}

This notification requires your attention as an administrator.

---
Rainbow Paws Admin Panel
This is an automated admin notification. Please do not reply to this email.
  `.trim();
}

/**
 * Ensure the admin_notifications table exists
 */
async function ensureAdminNotificationsTable(): Promise<boolean> {
  try {
    logger.debug('Checking if admin_notifications table exists...');
    // Check if the table exists
    const tableExists = await query(`
      SELECT COUNT(*) as count
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'admin_notifications'
    `) as any[];

    logger.debug('Table exists check result:', tableExists);

    if (tableExists[0].count === 0) {
      logger.debug('Creating admin_notifications table...');
      // Create the table if it doesn't exist
      await query(`
        CREATE TABLE IF NOT EXISTS admin_notifications (
          id INT AUTO_INCREMENT PRIMARY KEY,
          type VARCHAR(50) NOT NULL,
          title VARCHAR(255) NOT NULL,
          message TEXT NOT NULL,
          entity_type VARCHAR(50) DEFAULT NULL,
          entity_id INT DEFAULT NULL,
          link VARCHAR(255) DEFAULT NULL,
          is_read TINYINT(1) DEFAULT 0,
          created_at TIMESTAMP NOT NULL DEFAULT current_timestamp()
        )
      `);
      logger.debug('Admin_notifications table created successfully');
    } else {
      logger.debug('Admin_notifications table already exists');
    }

    return true;
  } catch (error) {
    logger.error("Error ensuring admin_notifications table exists:", error);
    return false;
  }
}
