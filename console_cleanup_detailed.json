{"totalFiles": 299, "filesWithConsole": 146, "totalConsoleStatements": 546, "byType": {"log": 59, "warn": 48, "error": 439, "info": 0, "debug": 0}, "byFile": [{"path": "src\\app\\admin\\dashboard\\page.tsx", "statements": [{"line": 79, "type": "error", "content": "console.error(`Primary dashboard endpoint failed: ${response.status} ${response.statusText}`);", "fullLine": "          console.error(`Primary dashboard endpoint failed: ${response.status} ${response.statusText}`);\r"}, {"line": 149, "type": "error", "content": "console.error('Dashboard data error:', result.error || 'Unknown error');", "fullLine": "          console.error('Dashboard data error:', result.error || 'Unknown error');\r"}, {"line": 154, "type": "error", "content": "console.error('Dashboard data fetch error:', errorMessage);", "fullLine": "        console.error('Dashboard data fetch error:', errorMessage);\r"}], "count": 3}, {"path": "src\\app\\admin\\profile\\page.tsx", "statements": [{"line": 77, "type": "error", "content": "console.error('Failed to load profile data');", "fullLine": "          console.error('Failed to load profile data');\r"}, {"line": 80, "type": "error", "content": "console.error('Failed to load profile data:', error);", "fullLine": "        console.error('Failed to load profile data:', error);\r"}], "count": 2}, {"path": "src\\app\\admin\\refunds\\page.tsx", "statements": [{"line": 81, "type": "error", "content": "console.error('Error fetching refunds:', error);", "fullLine": "      console.error('Error fetching refunds:', error);\r"}, {"line": 143, "type": "error", "content": "console.error('Error approving refund:', error);", "fullLine": "      console.error('Error approving refund:', error);\r"}, {"line": 173, "type": "error", "content": "console.error('Error denying refund:', error);", "fullLine": "      console.error('Error denying refund:', error);\r"}], "count": 3}, {"path": "src\\app\\admin\\reviews\\page.tsx", "statements": [{"line": 193, "type": "error", "content": "console.error('Error fetching booking details:', error);", "fullLine": "      console.error('Error fetching booking details:', error);\r"}], "count": 1}, {"path": "src\\app\\admin\\services\\page.tsx", "statements": [{"line": 188, "type": "error", "content": "console.error('Error fetching services:', err);", "fullLine": "        console.error('Error fetching services:', err);\r"}], "count": 1}, {"path": "src\\app\\admin\\settings\\page.tsx", "statements": [{"line": 59, "type": "error", "content": "console.error('Failed to load notification settings:', error);", "fullLine": "        console.error('Failed to load notification settings:', error);\r"}], "count": 1}, {"path": "src\\app\\admin\\users\\cremation\\page.tsx", "statements": [{"line": 165, "type": "log", "content": "console.log('Cremation centers data:', data.businesses);", "fullLine": "        console.log('Cremation centers data:', data.businesses);\r"}, {"line": 170, "type": "log", "content": "console.log(`Center ${center.name} has ${center.appeals.length} appeals:`, center.appeals);", "fullLine": "            console.log(`Center ${center.name} has ${center.appeals.length} appeals:`, center.appeals);\r"}, {"line": 173, "type": "log", "content": "console.log(`Center ${center.name} has ${pendingAppeals.length} pending appeals`);", "fullLine": "              console.log(`Center ${center.name} has ${pendingAppeals.length} pending appeals`);\r"}, {"line": 319, "type": "error", "content": "console.error('Error loading center appeals:', error);", "fullLine": "      console.error('Error loading center appeals:', error);\r"}, {"line": 355, "type": "error", "content": "console.error(`Error ${status} appeal:`, error);", "fullLine": "      console.error(`Error ${status} appeal:`, error);\r"}, {"line": 1021, "type": "log", "content": "// console.log(`Table row - Center ${center.name}: appeals=${center.appeals?.length || 0}, hasPendingAppeal=${hasPendingAppeal}`);", "fullLine": "                  // console.log(`Table row - Center ${center.name}: appeals=${center.appeals?.length || 0}, hasPendingAppeal=${hasPendingAppeal}`);\r"}], "count": 6}, {"path": "src\\app\\admin\\users\\furparents\\page.tsx", "statements": [{"line": 171, "type": "log", "content": "console.log('Fur parents data:', data.users);", "fullLine": "        console.log('Fur parents data:', data.users);\r"}, {"line": 176, "type": "log", "content": "console.log(`User ${user.first_name} ${user.last_name} has ${user.appeals.length} appeals:`, user.appeals);", "fullLine": "            console.log(`User ${user.first_name} ${user.last_name} has ${user.appeals.length} appeals:`, user.appeals);\r"}, {"line": 179, "type": "log", "content": "console.log(`User ${user.first_name} ${user.last_name} has ${pendingAppeals.length} pending appeals`);", "fullLine": "              console.log(`User ${user.first_name} ${user.last_name} has ${pendingAppeals.length} pending appeals`);\r"}, {"line": 278, "type": "error", "content": "console.error('Error loading user appeals:', error);", "fullLine": "      console.error('Error loading user appeals:', error);\r"}, {"line": 314, "type": "error", "content": "console.error(`Error ${status} appeal:`, error);", "fullLine": "      console.error(`Error ${status} appeal:`, error);\r"}], "count": 5}, {"path": "src\\app\\api\\admin\\bookings\\[id]\\refund\\route.ts", "statements": [{"line": 91, "type": "error", "content": "console.error('PayMongo refund error:', paymongoError);", "fullLine": "          console.error('PayMongo refund error:', paymongoError);\r"}, {"line": 125, "type": "error", "content": "console.error('Refund processing error:', refundError);", "fullLine": "      console.error('Refund processing error:', refundError);\r"}, {"line": 133, "type": "error", "content": "console.error('Admin refund error:', error);", "fullLine": "    console.error('Admin refund error:', error);\r"}, {"line": 204, "type": "error", "content": "console.error('Refund eligibility check error:', error);", "fullLine": "    console.error('Refund eligibility check error:', error);\r"}], "count": 4}, {"path": "src\\app\\api\\admin\\bookings\\[id]\\route.ts", "statements": [{"line": 75, "type": "error", "content": "console.error('Error fetching booking details:', error);", "fullLine": "    console.error('Error fetching booking details:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\admin\\create\\route.ts", "statements": [{"line": 148, "type": "error", "content": "console.error('Error creating admin user:', error);", "fullLine": "    console.error('Error creating admin user:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\admin\\cremation-businesses\\restrict\\route.ts", "statements": [{"line": 278, "type": "error", "content": "console.error('Failed to send restriction notification:', error);", "fullLine": "          console.error('Failed to send restriction notification:', error);\r"}, {"line": 314, "type": "warn", "content": "console.warn(`User not found for notification: ${userId}`);", "fullLine": "      console.warn(`User not found for notification: ${userId}`);\r"}, {"line": 338, "type": "error", "content": "console.error('Failed to create in-app notification:', notificationError);", "fullLine": "      console.error('Failed to create in-app notification:', notificationError);\r"}, {"line": 362, "type": "error", "content": "console.error('Failed to send email notification:', emailError);", "fullLine": "      console.error('Failed to send email notification:', emailError);\r"}, {"line": 379, "type": "error", "content": "console.error('Failed to send SMS notification:', smsError);", "fullLine": "        console.error('Failed to send SMS notification:', smsError);\r"}, {"line": 384, "type": "error", "content": "console.error('Error notifying user of restriction:', error);", "fullLine": "    console.error('Error notifying user of restriction:', error);\r"}], "count": 6}, {"path": "src\\app\\api\\admin\\cremation-businesses\\route.ts", "statements": [{"line": 316, "type": "log", "content": "console.log('Fetching appeals for business IDs:', businessIds);", "fullLine": "      console.log('Fetching appeals for business IDs:', businessIds);\r"}, {"line": 322, "type": "log", "content": "console.log('All appeals in database:', allAppeals);", "fullLine": "        console.log('All appeals in database:', allAppeals);\r"}, {"line": 340, "type": "log", "content": "console.log('Appeals found for businesses:', appeals);", "fullLine": "        console.log('Appeals found for businesses:', appeals);\r"}, {"line": 353, "type": "log", "content": "console.log('Grouped business appeals:', businessAppeals);", "fullLine": "        console.log('Grouped business appeals:', businessAppeals);\r"}, {"line": 356, "type": "error", "content": "console.error('Error fetching business appeals:', error);", "fullLine": "      console.error('Error fetching business appeals:', error);\r"}], "count": 5}, {"path": "src\\app\\api\\admin\\dashboard\\route.ts", "statements": [{"line": 11, "type": "error", "content": "console.error(`Query failed: ${queryString}`, error);", "fullLine": "    console.error(`Query failed: ${queryString}`, error);\r"}, {"line": 46, "type": "error", "content": "console.error('Database connection error:', error);", "fullLine": "      console.error('Database connection error:', error);\r"}, {"line": 140, "type": "error", "content": "console.error('Failed to fetch recent applications:', fallbackError);", "fullLine": "        console.error('Failed to fetch recent applications:', fallbackError);\r"}, {"line": 167, "type": "error", "content": "console.error('Failed to fetch pets count:', error);", "fullLine": "      console.error('Failed to fetch pets count:', error);\r"}, {"line": 187, "type": "error", "content": "console.error('Failed to fetch completed bookings count:', error);", "fullLine": "      console.error('Failed to fetch completed bookings count:', error);\r"}, {"line": 218, "type": "error", "content": "console.error('Failed to fetch pending applications:', error);", "fullLine": "      console.error('Failed to fetch pending applications:', error);\r"}, {"line": 244, "type": "error", "content": "console.error('Failed to fetch restricted cremation centers:', error);", "fullLine": "      console.error('Failed to fetch restricted cremation centers:', error);\r"}, {"line": 256, "type": "error", "content": "console.error('Failed to fetch previous month users count:', error);", "fullLine": "      console.error('Failed to fetch previous month users count:', error);\r"}, {"line": 268, "type": "error", "content": "console.error('Failed to fetch previous month services count:', error);", "fullLine": "      console.error('Failed to fetch previous month services count:', error);\r"}, {"line": 310, "type": "error", "content": "console.error('Failed to fetch previous month applications:', error);", "fullLine": "      console.error('Failed to fetch previous month applications:', error);\r"}, {"line": 326, "type": "error", "content": "console.error('Failed to fetch pending applications count:', error);", "fullLine": "      console.error('Failed to fetch pending applications count:', error);\r"}], "count": 11}, {"path": "src\\app\\api\\admin\\logs\\stats\\route.ts", "statements": [{"line": 132, "type": "error", "content": "console.error('Error fetching log stats:', error);", "fullLine": "    console.error('Error fetching log stats:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\admin\\notification-preferences\\route.ts", "statements": [{"line": 36, "type": "error", "content": "console.error('Error ensuring notification columns exist:', error);", "fullLine": "    console.error('Error ensuring notification columns exist:', error);\r"}, {"line": 81, "type": "error", "content": "console.error('Error fetching admin notification preferences:', error);", "fullLine": "    console.error('Error fetching admin notification preferences:', error);\r"}, {"line": 133, "type": "error", "content": "console.error('Error updating admin notification preferences:', error);", "fullLine": "    console.error('Error updating admin notification preferences:', error);\r"}], "count": 3}, {"path": "src\\app\\api\\admin\\notifications\\route.ts", "statements": [{"line": 274, "type": "error", "content": "console.error('Database error marking admin notification as read:', dbError);", "fullLine": "      console.error('Database error marking admin notification as read:', dbError);\r"}, {"line": 289, "type": "error", "content": "console.error('Error marking admin notification as read:', error);", "fullLine": "    console.error('Error marking admin notification as read:', error);\r"}], "count": 2}, {"path": "src\\app\\api\\admin\\notifications\\[id]\\route.ts", "statements": [{"line": 85, "type": "error", "content": "console.error('Error deleting admin notification:', error);", "fullLine": "    console.error('Error deleting admin notification:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\admin\\payments\\analytics\\route.ts", "statements": [{"line": 202, "type": "error", "content": "console.error('Error fetching payment analytics:', error);", "fullLine": "    console.error('Error fetching payment analytics:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\admin\\profile\\ensure-table.ts", "statements": [{"line": 35, "type": "error", "content": "console.error('Error ensuring admin_profiles table exists:', error);", "fullLine": "    console.error('Error ensuring admin_profiles table exists:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\admin\\profile\\route.ts", "statements": [{"line": 84, "type": "error", "content": "console.error('Error fetching admin profile:', error);", "fullLine": "    console.error('Error fetching admin profile:', error);\r"}, {"line": 268, "type": "error", "content": "console.error('Error updating admin profile:', error);", "fullLine": "    console.error('Error updating admin profile:', error);\r"}], "count": 2}, {"path": "src\\app\\api\\admin\\refunds\\retry-failed\\route.ts", "statements": [{"line": 72, "type": "error", "content": "console.error(`Error validating booking ${booking.id}:`, error);", "fullLine": "          console.error(`Error validating booking ${booking.id}:`, error);\r"}, {"line": 92, "type": "error", "content": "console.error('Error in retry failed refunds:', error);", "fullLine": "    console.error('Error in retry failed refunds:', error);\r"}, {"line": 170, "type": "error", "content": "console.error('Error getting retry status:', error);", "fullLine": "    console.error('Error getting retry status:', error);\r"}], "count": 3}, {"path": "src\\app\\api\\admin\\refunds\\route.ts", "statements": [{"line": 111, "type": "error", "content": "console.error('Error fetching refunds:', error);", "fullLine": "    console.error('Error fetching refunds:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\admin\\refunds\\[id]\\approve\\route.ts", "statements": [{"line": 131, "type": "error", "content": "console.error('Failed to send approval email:', emailError);", "fullLine": "                console.error('Failed to send approval email:', emailError);\r"}, {"line": 145, "type": "error", "content": "console.error('Failed to create user notification:', notificationError);", "fullLine": "              console.error('Failed to create user notification:', notificationError);\r"}, {"line": 158, "type": "error", "content": "console.error('Failed to create admin notification:', adminNotificationError);", "fullLine": "               console.error('Failed to create admin notification:', adminNotificationError);\r"}, {"line": 175, "type": "error", "content": "console.error('PayMongo refund error:', paymongoError);", "fullLine": "            console.error('PayMongo refund error:', paymongoError);\r"}, {"line": 200, "type": "error", "content": "console.error('Failed to send completion email:', emailError);", "fullLine": "                console.error('Failed to send completion email:', emailError);\r"}, {"line": 214, "type": "error", "content": "console.error('Failed to create user notification:', notificationError);", "fullLine": "              console.error('Failed to create user notification:', notificationError);\r"}, {"line": 227, "type": "error", "content": "console.error('Failed to create admin notification:', adminNotificationError);", "fullLine": "               console.error('Failed to create admin notification:', adminNotificationError);\r"}, {"line": 269, "type": "error", "content": "console.error('Failed to send completion email:', emailError);", "fullLine": "              console.error('Failed to send completion email:', emailError);\r"}, {"line": 283, "type": "error", "content": "console.error('Failed to create user notification:', notificationError);", "fullLine": "            console.error('Failed to create user notification:', notificationError);\r"}, {"line": 296, "type": "error", "content": "console.error('Failed to create admin notification:', adminNotificationError);", "fullLine": "            console.error('Failed to create admin notification:', adminNotificationError);\r"}, {"line": 337, "type": "error", "content": "console.error('Failed to send completion email:', emailError);", "fullLine": "            console.error('Failed to send completion email:', emailError);\r"}, {"line": 351, "type": "error", "content": "console.error('Failed to create user notification:', notificationError);", "fullLine": "          console.error('Failed to create user notification:', notificationError);\r"}, {"line": 364, "type": "error", "content": "console.error('Failed to create admin notification:', adminNotificationError);", "fullLine": "          console.error('Failed to create admin notification:', adminNotificationError);\r"}, {"line": 382, "type": "error", "content": "console.error('Refund processing error:', processingError);", "fullLine": "      console.error('Refund processing error:', processingError);\r"}, {"line": 390, "type": "error", "content": "console.error('Refund approval error:', error);", "fullLine": "    console.error('Refund approval error:', error);\r"}, {"line": 441, "type": "error", "content": "console.error('Failed to notify service provider about refund:', error);", "fullLine": "    console.error('Failed to notify service provider about refund:', error);\r"}], "count": 16}, {"path": "src\\app\\api\\admin\\refunds\\[id]\\deny\\route.ts", "statements": [{"line": 137, "type": "error", "content": "console.error('Failed to send denial email:', emailError);", "fullLine": "        console.error('Failed to send denial email:', emailError);\r"}, {"line": 151, "type": "error", "content": "console.error('Failed to create admin notification:', adminNotificationError);", "fullLine": "      console.error('Failed to create admin notification:', adminNotificationError);\r"}, {"line": 166, "type": "error", "content": "console.error('Refund denial error:', error);", "fullLine": "    console.error('Refund denial error:', error);\r"}], "count": 3}, {"path": "src\\app\\api\\admin\\refunds\\[id]\\retry\\route.ts", "statements": [{"line": 74, "type": "error", "content": "console.error('Refund retry failed:', retryError);", "fullLine": "      console.error('Refund retry failed:', retryError);\r"}, {"line": 84, "type": "error", "content": "console.error('Refund retry error:', error);", "fullLine": "    console.error('Refund retry error:', error);\r"}], "count": 2}, {"path": "src\\app\\api\\admin\\reviews\\route.ts", "statements": [{"line": 34, "type": "error", "content": "console.error('Error checking reviews table:', tableError);", "fullLine": "      console.error('Error checking reviews table:', tableError);\r"}], "count": 1}, {"path": "src\\app\\api\\admin\\reviews\\[id]\\route.ts", "statements": [{"line": 54, "type": "error", "content": "console.error('Error deleting review:', error);", "fullLine": "    console.error('Error deleting review:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\admin\\services\\listing\\route.ts", "statements": [{"line": 106, "type": "error", "content": "console.error('Unexpected query result format:', result);", "fullLine": "      console.error('Unexpected query result format:', result);\r"}, {"line": 110, "type": "error", "content": "console.error('Primary query failed:', err);", "fullLine": "    console.error('Primary query failed:', err);\r"}, {"line": 132, "type": "error", "content": "console.error('Fallback query also failed:', fallbackError);", "fullLine": "      console.error('Fallback query also failed:', fallbackError);\r"}, {"line": 164, "type": "error", "content": "console.error('Error calculating revenue:', error);", "fullLine": "    console.error('Error calculating revenue:', error);\r"}, {"line": 198, "type": "error", "content": "console.error('Error fetching inclusions:', error);", "fullLine": "      console.error('Error fetching inclusions:', error);\r"}, {"line": 222, "type": "error", "content": "console.error('Error fetching addons:', error);", "fullLine": "      console.error('Error fetching addons:', error);\r"}, {"line": 253, "type": "error", "content": "console.error('Error fetching reviews data:', error);", "fullLine": "      console.error('Error fetching reviews data:', error);\r"}, {"line": 297, "type": "error", "content": "console.error('Error fetching package images:', error);", "fullLine": "    console.error('Error fetching package images:', error);\r"}, {"line": 360, "type": "error", "content": "console.error('Error fetching total bookings:', error);", "fullLine": "    console.error('Error fetching total bookings:', error);\r"}, {"line": 399, "type": "error", "content": "console.error('Error fetching verified centers:', error);", "fullLine": "    console.error('Error fetching verified centers:', error);\r"}], "count": 10}, {"path": "src\\app\\api\\admin\\upload-profile-picture\\route.ts", "statements": [{"line": 40, "type": "error", "content": "console.error('Error saving admin profile picture:', error);", "fullLine": "    console.error('Error saving admin profile picture:', error);\r"}, {"line": 115, "type": "error", "content": "console.error('Error cleaning up old admin profile pictures:', cleanupError);", "fullLine": "      console.error('Error cleaning up old admin profile pictures:', cleanupError);\r"}, {"line": 141, "type": "error", "content": "console.error('Error updating profile picture in database:', dbError);", "fullLine": "      console.error('Error updating profile picture in database:', dbError);\r"}, {"line": 148, "type": "error", "content": "console.error('Error in admin profile picture upload:', error);", "fullLine": "    console.error('Error in admin profile picture upload:', error);\r"}], "count": 4}, {"path": "src\\app\\api\\admin\\users\\restrict\\route.ts", "statements": [{"line": 421, "type": "error", "content": "console.error('Error notifying user of restriction:', error);", "fullLine": "    console.error('Error notifying user of restriction:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\admins\\[id]\\route.ts", "statements": [{"line": 49, "type": "error", "content": "console.error('Error in admin API:', error);", "fullLine": "    console.error('Error in admin API:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\appeals\\route.ts", "statements": [{"line": 55, "type": "error", "content": "console.error('Error creating appeals tables:', error);", "fullLine": "    console.error('Error creating appeals tables:', error);\r"}, {"line": 153, "type": "error", "content": "console.error('Failed to notify admins of new appeal:', error);", "fullLine": "      console.error('Failed to notify admins of new appeal:', error);\r"}, {"line": 158, "type": "log", "content": "console.log('Creating admin panel notification for appeal:', result);", "fullLine": "      console.log('Creating admin panel notification for appeal:', result);\r"}, {"line": 178, "type": "log", "content": "console.log('Admin panel notification result:', notificationResult);", "fullLine": "      console.log('Admin panel notification result:', notificationResult);\r"}, {"line": 180, "type": "error", "content": "console.error('Failed to create admin panel notification:', adminNotificationError);", "fullLine": "      console.error('Failed to create admin panel notification:', adminNotificationError);\r"}, {"line": 190, "type": "error", "content": "console.error('<PERSON><PERSON><PERSON> submitting appeal:', error);", "fullLine": "    console.error('<PERSON><PERSON><PERSON> submitting appeal:', error);\r"}, {"line": 285, "type": "error", "content": "console.error('Error fetching appeals:', error);", "fullLine": "    console.error('Error fetching appeals:', error);\r"}, {"line": 296, "type": "log", "content": "console.log(`Starting admin notification process for appeal ${appealId}`);", "fullLine": "    console.log(`Starting admin notification process for appeal ${appealId}`);\r"}, {"line": 305, "type": "log", "content": "console.log(`Found ${admins.length} admin users to notify`);", "fullLine": "    console.log(`Found ${admins.length} admin users to notify`);\r"}, {"line": 308, "type": "warn", "content": "console.warn('No admin users found to notify about new appeal');", "fullLine": "      console.warn('No admin users found to notify about new appeal');\r"}, {"line": 325, "type": "log", "content": "console.log(`Notifying admin ${admin.user_id} (${admin.email}) about appeal ${appealId}`);", "fullLine": "          console.log(`Notifying admin ${admin.user_id} (${admin.email}) about appeal ${appealId}`);\r"}, {"line": 337, "type": "error", "content": "console.error(`Failed to create in-app notification for admin ${admin.user_id}:`, notificationResult.error);", "fullLine": "            console.error(`Failed to create in-app notification for admin ${admin.user_id}:`, notificationResult.error);\r"}, {"line": 347, "type": "log", "content": "console.log(`Email sent successfully to admin ${admin.email}`);", "fullLine": "            console.log(`Email sent successfully to admin ${admin.email}`);\r"}, {"line": 349, "type": "error", "content": "console.error(`Failed to send email to admin ${admin.email}:`, emailError);", "fullLine": "            console.error(`Failed to send email to admin ${admin.email}:`, emailError);\r"}, {"line": 359, "type": "log", "content": "console.log(`SMS sent successfully to admin ${admin.phone}`);", "fullLine": "              console.log(`SMS sent successfully to admin ${admin.phone}`);\r"}, {"line": 361, "type": "error", "content": "console.error(`Failed to send SMS to admin ${admin.phone}:`, smsError);", "fullLine": "              console.error(`Failed to send SMS to admin ${admin.phone}:`, smsError);\r"}, {"line": 367, "type": "error", "content": "console.error(`Failed to notify admin ${admin.user_id}:`, adminError);", "fullLine": "          console.error(`Failed to notify admin ${admin.user_id}:`, adminError);\r"}, {"line": 377, "type": "log", "content": "console.log(`Admin notification results for appeal ${appealId}: ${successful} successful, ${failed} failed`);", "fullLine": "    console.log(`Admin notification results for appeal ${appealId}: ${successful} successful, ${failed} failed`);\r"}, {"line": 380, "type": "warn", "content": "console.warn(`Some admin notifications failed for appeal ${appealId}`,", "fullLine": "      console.warn(`Some admin notifications failed for appeal ${appealId}`,\r"}, {"line": 386, "type": "error", "content": "console.error('Error in notifyAdminsOfNewAppeal function:', error);", "fullLine": "    console.error('Error in notifyAdminsOfNewAppeal function:', error);\r"}], "count": 20}, {"path": "src\\app\\api\\appeals\\[id]\\history\\route.ts", "statements": [{"line": 59, "type": "error", "content": "console.error('Error fetching appeal history:', error);", "fullLine": "    console.error('Error fetching appeal history:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\appeals\\[id]\\route.ts", "statements": [{"line": 63, "type": "error", "content": "console.error('Error fetching appeal:', error);", "fullLine": "    console.error('Error fetching appeal:', error);\r"}, {"line": 216, "type": "error", "content": "console.error('Error updating appeal:', error);", "fullLine": "    console.error('Error updating appeal:', error);\r"}, {"line": 266, "type": "error", "content": "console.error('Error deleting appeal:', error);", "fullLine": "    console.error('Error deleting appeal:', error);\r"}, {"line": 351, "type": "error", "content": "console.error('Error notifying user of appeal update:', error);", "fullLine": "    console.error('Error notifying user of appeal update:', error);\r"}], "count": 4}, {"path": "src\\app\\api\\auth\\check\\route.ts", "statements": [{"line": 23, "type": "error", "content": "console.error('Auth check error:', error);", "fullLine": "    console.error('Auth check error:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\auth\\check-business-status\\route.ts", "statements": [{"line": 73, "type": "error", "content": "console.error('Error checking business status:', error);", "fullLine": "    console.error('Error checking business status:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\auth\\check-user-status\\route.ts", "statements": [{"line": 68, "type": "error", "content": "console.error('Error checking user status:', error);", "fullLine": "    console.error('Error checking user status:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\auth\\login\\route.ts", "statements": [{"line": 79, "type": "error", "content": "console.error('Error querying user:', queryError);", "fullLine": "      console.error('Error querying user:', queryError);\r"}], "count": 1}, {"path": "src\\app\\api\\auth\\logout\\route.ts", "statements": [{"line": 19, "type": "error", "content": "console.error('Logout error:', error);", "fullLine": "    console.error('Logout error:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\auth\\register\\route.ts", "statements": [{"line": 245, "type": "log", "content": "console.log(\"Inserting user with values:\", {", "fullLine": "          console.log(\"Inserting user with values:\", {\r"}, {"line": 305, "type": "log", "content": "console.log(\"Service provider values:\", {", "fullLine": "            console.log(\"Service provider values:\", {\r"}, {"line": 338, "type": "error", "content": "console.error(\"Error executing service provider insertion query:\", queryError);", "fullLine": "              console.error(\"Error executing service provider insertion query:\", queryError);\r"}, {"line": 361, "type": "error", "content": "console.error(\"Failed to create admin notification:\", notificationResult.error);", "fullLine": "                console.error(\"Failed to create admin notification:\", notificationResult.error);\r"}, {"line": 364, "type": "error", "content": "console.error(\"Error creating admin notification:\", notificationError);", "fullLine": "              console.error(\"Error creating admin notification:\", notificationError);\r"}, {"line": 369, "type": "error", "content": "console.error(\"Error creating service provider:\", serviceProviderError);", "fullLine": "            console.error(\"Error creating service provider:\", serviceProviderError);\r"}, {"line": 385, "type": "error", "content": "console.error(\"Registration process failed:\", regError);", "fullLine": "      console.error(\"Registration process failed:\", regError);\r"}, {"line": 398, "type": "warn", "content": "console.warn(\"Failed to send welcome email:\", emailResult);", "fullLine": "          console.warn(\"Failed to send welcome email:\", emailResult);\r"}, {"line": 403, "type": "error", "content": "console.error(\"Error sending welcome email:\", emailError);", "fullLine": "        console.error(\"Error sending welcome email:\", emailError);\r"}, {"line": 425, "type": "warn", "content": "console.warn(\"Failed to generate OTP:\", otpResult);", "fullLine": "            console.warn(\"Failed to generate OTP:\", otpResult);\r"}, {"line": 436, "type": "error", "content": "console.error(\"Error updating fur parent verification status:\", updateError);", "fullLine": "              console.error(\"Error updating fur parent verification status:\", updateError);\r"}, {"line": 448, "type": "error", "content": "console.error(\"Error updating verification status:\", updateError);", "fullLine": "            console.error(\"Error updating verification status:\", updateError);\r"}, {"line": 453, "type": "error", "content": "console.error(\"Error in OTP generation/verification process:\", otpError);", "fullLine": "        console.error(\"Error in OTP generation/verification process:\", otpError);\r"}, {"line": 470, "type": "error", "content": "console.error(\"Registration failed with error:\", error);", "fullLine": "    console.error(\"Registration failed with error:\", error);\r"}, {"line": 474, "type": "error", "content": "console.error(\"Error message:\", error.message);", "fullLine": "      console.error(\"Error message:\", error.message);\r"}, {"line": 475, "type": "error", "content": "console.error(\"Error stack:\", error.stack);", "fullLine": "      console.error(\"Error stack:\", error.stack);\r"}], "count": 16}, {"path": "src\\app\\api\\bookings\\route.ts", "statements": [{"line": 152, "type": "error", "content": "console.error('Error parsing booking date:', dateError);", "fullLine": "              console.error('Error parsing booking date:', dateError);\r"}, {"line": 200, "type": "error", "content": "console.error('Provider fetch error:', providerError);", "fullLine": "              console.error('Provider fetch error:', providerError);\r"}, {"line": 523, "type": "error", "content": "console.error('Error parsing booking date:', dateError);", "fullLine": "                    console.error('Error parsing booking date:', dateError);\r"}, {"line": 571, "type": "error", "content": "console.error('Provider fetch error:', providerError);", "fullLine": "                    console.error('Provider fetch error:', providerError);\r"}, {"line": 650, "type": "error", "content": "console.error('Error parsing booking date:', dateError);", "fullLine": "            console.error('Error parsing booking date:', dateError);\r"}, {"line": 694, "type": "error", "content": "console.error('Provider fetch error:', providerError);", "fullLine": "            console.error('Provider fetch error:', providerError);\r"}, {"line": 1178, "type": "error", "content": "console.error('Error removing time slot after booking creation:', timeSlotError);", "fullLine": "        console.error('Error removing time slot after booking creation:', timeSlotError);\r"}], "count": 7}, {"path": "src\\app\\api\\bookings\\[id]\\cancel\\route.ts", "statements": [{"line": 76, "type": "error", "content": "console.error('Error fetching booking details:', error);", "fullLine": "      console.error('Error fetching booking details:', error);\r"}, {"line": 103, "type": "error", "content": "console.error('Database update error:', dbError);", "fullLine": "      console.error('Database update error:', dbError);\r"}, {"line": 134, "type": "error", "content": "console.error('Failed to create admin notification:', result.error);", "fullLine": "                  console.error('Failed to create admin notification:', result.error);\r"}, {"line": 138, "type": "error", "content": "console.error('Failed to create admin notification:', notificationError);", "fullLine": "                console.error('Failed to create admin notification:', notificationError);\r"}, {"line": 167, "type": "error", "content": "console.error('Refund email error:', emailError);", "fullLine": "              console.error('Refund email error:', emailError);\r"}, {"line": 172, "type": "error", "content": "console.error('Refund request error:', refundError);", "fullLine": "        console.error('Refund request error:', refundError);\r"}, {"line": 183, "type": "error", "content": "console.error('Error sending cancellation notifications:', notificationError);", "fullLine": "      console.error('Error sending cancellation notifications:', notificationError);\r"}], "count": 7}, {"path": "src\\app\\api\\bookings\\[id]\\refund\\route.ts", "statements": [{"line": 105, "type": "error", "content": "console.error('Failed to create admin notification:', result.error);", "fullLine": "        console.error('Failed to create admin notification:', result.error);\r"}, {"line": 109, "type": "error", "content": "console.error('Failed to create admin notification:', notificationError);", "fullLine": "      console.error('Failed to create admin notification:', notificationError);\r"}, {"line": 141, "type": "error", "content": "console.error('Failed to send refund confirmation email:', emailError);", "fullLine": "      console.error('Failed to send refund confirmation email:', emailError);\r"}, {"line": 158, "type": "error", "content": "console.error('Refund request error:', error);", "fullLine": "    console.error('Refund request error:', error);\r"}, {"line": 240, "type": "error", "content": "console.error('Refund eligibility check error:', error);", "fullLine": "    console.error('Refund eligibility check error:', error);\r"}], "count": 5}, {"path": "src\\app\\api\\businesses\\applications\\[id]\\approve\\route.ts", "statements": [{"line": 138, "type": "error", "content": "console.error('Error creating approval notification:', notificationError);", "fullLine": "      console.error('Error creating approval notification:', notificationError);\r"}, {"line": 158, "type": "error", "content": "console.error('Error logging admin action:', logError);", "fullLine": "      console.error('Error logging admin action:', logError);\r"}, {"line": 179, "type": "warn", "content": "console.warn('Email sending failed:', emailResult.error);", "fullLine": "          console.warn('Email sending failed:', emailResult.error);\r"}, {"line": 183, "type": "error", "content": "console.error('Error sending verification email:', emailError);", "fullLine": "        console.error('Error sending verification email:', emailError);\r"}, {"line": 201, "type": "error", "content": "console.error('Error creating business notification:', notificationError);", "fullLine": "        console.error('Error creating business notification:', notificationError);\r"}, {"line": 212, "type": "error", "content": "console.error('Error approving application:', error);", "fullLine": "    console.error('Error approving application:', error);\r"}], "count": 6}, {"path": "src\\app\\api\\businesses\\applications\\[id]\\decline\\route.ts", "statements": [{"line": 156, "type": "warn", "content": "console.warn('Email sending failed:', emailResult.error);", "fullLine": "          console.warn('Email sending failed:', emailResult.error);\r"}, {"line": 160, "type": "error", "content": "console.error('Error sending verification email:', emailError);", "fullLine": "        console.error('Error sending verification email:', emailError);\r"}, {"line": 179, "type": "error", "content": "console.error('Error creating business notification:', notificationError);", "fullLine": "        console.error('Error creating business notification:', notificationError);\r"}, {"line": 201, "type": "error", "content": "console.error('Error logging admin action:', logError);", "fullLine": "      console.error('Error logging admin action:', logError);\r"}, {"line": 211, "type": "error", "content": "console.error('Error declining application:', error);", "fullLine": "    console.error('Error declining application:', error);\r"}], "count": 5}, {"path": "src\\app\\api\\businesses\\upload-documents\\route.ts", "statements": [{"line": 39, "type": "error", "content": "console.error(`Failed to save ${documentType}:`, error);", "fullLine": "    console.error(`Failed to save ${documentType}:`, error);\r"}], "count": 1}, {"path": "src\\app\\api\\cart-bookings\\route.ts", "statements": [{"line": 266, "type": "error", "content": "console.error('Error removing time slot after booking creation:', timeSlotError);", "fullLine": "      console.error('Error removing time slot after booking creation:', timeSlotError);\r"}, {"line": 276, "type": "error", "content": "console.error('Booking creation error:', error);", "fullLine": "    console.error('Booking creation error:', error);\r"}], "count": 2}, {"path": "src\\app\\api\\cremation\\availability\\batch\\route.ts", "statements": [{"line": 182, "type": "warn", "content": "console.warn(`Invalid service ID: ${serviceId}, skipping`);", "fullLine": "                      console.warn(`Invalid service ID: ${serviceId}, skipping`);\r"}, {"line": 193, "type": "warn", "content": "console.warn(`Failed to link service ${serviceId} to time slot ${slotResult.insertId}:`, serviceError);", "fullLine": "                      console.warn(`Failed to link service ${serviceId} to time slot ${slotResult.insertId}:`, serviceError);\r"}, {"line": 204, "type": "error", "content": "console.error(`Error processing day ${dayData.date}:`, dayError);", "fullLine": "          console.error(`Error processing day ${dayData.date}:`, dayError);\r"}, {"line": 229, "type": "error", "content": "console.error('Batch availability update error:', error);", "fullLine": "    console.error('Batch availability update error:', error);\r"}], "count": 4}, {"path": "src\\app\\api\\cremation\\availability\\route.ts", "statements": [{"line": 318, "type": "error", "content": "console.error('Availability update error:', error);", "fullLine": "    console.error('Availability update error:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\cremation\\availability\\timeslot\\route.ts", "statements": [{"line": 99, "type": "error", "content": "console.error('Delete operation did not affect any rows');", "fullLine": "        console.error('Delete operation did not affect any rows');\r"}, {"line": 124, "type": "error", "content": "console.error('Time slot deletion error:', error);", "fullLine": "    console.error('Time slot deletion error:', error);\r"}], "count": 2}, {"path": "src\\app\\api\\cremation\\bookings\\route.ts", "statements": [{"line": 265, "type": "error", "content": "console.error('Error fetching booking add-ons:', error);", "fullLine": "        console.error('Error fetching booking add-ons:', error);\r"}, {"line": 320, "type": "error", "content": "console.error('Error in GET /api/cremation/bookings:', error);", "fullLine": "    console.error('Error in GET /api/cremation/bookings:', error);\r"}, {"line": 338, "type": "error", "content": "console.error('Database connection failed:', dbError);", "fullLine": "      console.error('Database connection failed:', dbError);\r"}, {"line": 601, "type": "error", "content": "console.error('Error inserting add-ons:', addOnError);", "fullLine": "          console.error('Error inserting add-ons:', addOnError);\r"}, {"line": 639, "type": "error", "content": "console.error('Error removing time slot after booking creation:', timeSlotError);", "fullLine": "      console.error('Error removing time slot after booking creation:', timeSlotError);\r"}, {"line": 653, "type": "error", "content": "console.error('Error creating booking notifications:', notificationError);", "fullLine": "      console.error('Error creating booking notifications:', notificationError);\r"}, {"line": 664, "type": "error", "content": "console.error('Error in POST /api/cremation/bookings:', error);", "fullLine": "    console.error('Error in POST /api/cremation/bookings:', error);\r"}], "count": 7}, {"path": "src\\app\\api\\cremation\\bookings\\[id]\\payment\\route.ts", "statements": [{"line": 110, "type": "error", "content": "console.error('Error creating payment notification:', notificationError);", "fullLine": "      console.error('Error creating payment notification:', notificationError);\r"}], "count": 1}, {"path": "src\\app\\api\\cremation\\bookings\\[id]\\route.ts", "statements": [{"line": 92, "type": "error", "content": "console.error('Error fetching booking details:', error);", "fullLine": "    console.error('Error fetching booking details:', error);\r"}, {"line": 162, "type": "error", "content": "console.error('Failed to create admin notification:', adminNotificationError);", "fullLine": "                  console.error('Failed to create admin notification:', adminNotificationError);\r"}, {"line": 174, "type": "error", "content": "console.error('Error processing refund for cancelled booking:', refundError);", "fullLine": "          console.error('Error processing refund for cancelled booking:', refundError);\r"}, {"line": 217, "type": "error", "content": "console.error('Error sending notification for booking status update:', notificationError);", "fullLine": "        console.error('Error sending notification for booking status update:', notificationError);\r"}, {"line": 232, "type": "error", "content": "console.error('Error updating booking status:', error);", "fullLine": "    console.error('Error updating booking status:', error);\r"}], "count": 5}, {"path": "src\\app\\api\\cremation\\dashboard\\route.ts", "statements": [{"line": 359, "type": "error", "content": "console.error('Dashboard data fetch error:', error instanceof Error ? error.message : 'Unknown error');", "fullLine": "    console.error('Dashboard data fetch error:', error instanceof Error ? error.message : 'Unknown error');\r"}], "count": 1}, {"path": "src\\app\\api\\cremation\\history\\route.ts", "statements": [{"line": 99, "type": "error", "content": "console.error('Error creating service_bookings table:', createError);", "fullLine": "        console.error('Error creating service_bookings table:', createError);\r"}], "count": 1}, {"path": "src\\app\\api\\cremation\\notification-preferences\\route.ts", "statements": [{"line": 36, "type": "error", "content": "console.error('Error ensuring notification columns exist:', error);", "fullLine": "    console.error('Error ensuring notification columns exist:', error);\r"}, {"line": 81, "type": "error", "content": "console.error('Error fetching cremation provider notification preferences:', error);", "fullLine": "    console.error('Error fetching cremation provider notification preferences:', error);\r"}, {"line": 133, "type": "error", "content": "console.error('Error updating cremation provider notification preferences:', error);", "fullLine": "    console.error('Error updating cremation provider notification preferences:', error);\r"}], "count": 3}, {"path": "src\\app\\api\\cremation\\notifications\\check-pending\\route.ts", "statements": [{"line": 49, "type": "error", "content": "console.error('Error checking pending bookings:', error);", "fullLine": "    console.error('Error checking pending bookings:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\cremation\\notifications\\pending-bookings\\route.ts", "statements": [{"line": 50, "type": "error", "content": "console.error('Error fetching pending bookings:', error);", "fullLine": "        console.error('Error fetching pending bookings:', error);\r"}, {"line": 76, "type": "error", "content": "console.error('Error fetching recent pending bookings:', error);", "fullLine": "        console.error('Error fetching recent pending bookings:', error);\r"}, {"line": 87, "type": "error", "content": "console.error('Error in pending bookings check:', error);", "fullLine": "      console.error('Error in pending bookings check:', error);\r"}, {"line": 95, "type": "error", "content": "console.error('Error in pending bookings API:', error);", "fullLine": "    console.error('Error in pending bookings API:', error);\r"}, {"line": 135, "type": "error", "content": "console.error('Error marking bookings as notified:', error);", "fullLine": "    console.error('Error marking bookings as notified:', error);\r"}], "count": 5}, {"path": "src\\app\\api\\cremation\\notifications\\route.ts", "statements": [{"line": 38, "type": "warn", "content": "console.warn('Could not describe notifications table:', describeError);", "fullLine": "      console.warn('Could not describe notifications table:', describeError);\r"}, {"line": 88, "type": "error", "content": "console.error('Fetch cremation provider notifications error:', error);", "fullLine": "    console.error('Fetch cremation provider notifications error:', error);\r"}, {"line": 127, "type": "warn", "content": "console.warn('Could not describe notifications table:', describeError);", "fullLine": "      console.warn('Could not describe notifications table:', describeError);\r"}, {"line": 164, "type": "error", "content": "console.error('Mark cremation provider notifications as read error:', error);", "fullLine": "    console.error('Mark cremation provider notifications as read error:', error);\r"}, {"line": 203, "type": "warn", "content": "console.warn('Could not describe notifications table:', describeError);", "fullLine": "      console.warn('Could not describe notifications table:', describeError);\r"}, {"line": 240, "type": "error", "content": "console.error('Delete cremation provider notifications error:', error);", "fullLine": "    console.error('Delete cremation provider notifications error:', error);\r"}], "count": 6}, {"path": "src\\app\\api\\cremation\\notifications\\[id]\\route.ts", "statements": [{"line": 41, "type": "warn", "content": "console.warn('Could not describe notifications table:', describeError);", "fullLine": "      console.warn('Could not describe notifications table:', describeError);\r"}, {"line": 72, "type": "error", "content": "console.error('Get cremation provider notification error:', error);", "fullLine": "    console.error('Get cremation provider notification error:', error);\r"}, {"line": 116, "type": "warn", "content": "console.warn('Could not describe notifications table:', describeError);", "fullLine": "      console.warn('Could not describe notifications table:', describeError);\r"}, {"line": 138, "type": "error", "content": "console.error('Mark cremation provider notification as read error:', error);", "fullLine": "    console.error('Mark cremation provider notification as read error:', error);\r"}], "count": 4}, {"path": "src\\app\\api\\cremation\\profile\\route.ts", "statements": [{"line": 70, "type": "error", "content": "console.error('Error fetching cremation provider profile:', error);", "fullLine": "    console.error('Error fetching cremation provider profile:', error);\r"}, {"line": 144, "type": "error", "content": "console.error('Error updating cremation provider profile:', error);", "fullLine": "    console.error('Error updating cremation provider profile:', error);\r"}], "count": 2}, {"path": "src\\app\\api\\cremation\\upload-profile-picture\\route.ts", "statements": [{"line": 40, "type": "error", "content": "console.error(`Failed to save profile picture:`, error);", "fullLine": "    console.error(`Failed to save profile picture:`, error);\r"}, {"line": 108, "type": "error", "content": "console.error('Error cleaning up old profile pictures:', cleanupError);", "fullLine": "      console.error('Error cleaning up old profile pictures:', cleanupError);\r"}, {"line": 119, "type": "error", "content": "console.error('Failed to update profile picture in database:', dbError);", "fullLine": "      console.error('Failed to update profile picture in database:', dbError);\r"}, {"line": 130, "type": "error", "content": "console.error('Error uploading profile picture:', error);", "fullLine": "    console.error('Error uploading profile picture:', error);\r"}], "count": 4}, {"path": "src\\app\\api\\db-health\\route.ts", "statements": [{"line": 60, "type": "error", "content": "console.error('Database health check error:', error);", "fullLine": "    console.error('Database health check error:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\image\\[...path]\\route.ts", "statements": [{"line": 162, "type": "error", "content": "console.error('Error serving image:', error);", "fullLine": "    console.error('Error serving image:', error);\r"}, {"line": 184, "type": "error", "content": "console.error('Error serving fallback image:', fallbackError);", "fullLine": "      console.error('Error serving fallback image:', fallbackError);\r"}], "count": 2}, {"path": "src\\app\\api\\notifications\\mark-read\\route.ts", "statements": [{"line": 84, "type": "warn", "content": "console.warn('Could not describe notifications table:', describeError);", "fullLine": "      console.warn('Could not describe notifications table:', describeError);\r"}, {"line": 126, "type": "error", "content": "console.error('Database error in mark-read:', dbError);", "fullLine": "      console.error('Database error in mark-read:', dbError);\r"}, {"line": 142, "type": "error", "content": "console.error('Unexpected error in mark-read:', error);", "fullLine": "    console.error('Unexpected error in mark-read:', error);\r"}], "count": 3}, {"path": "src\\app\\api\\notifications\\process-reminders\\route.ts", "statements": [{"line": 45, "type": "error", "content": "console.error('Error processing reminders:', error);", "fullLine": "    console.error('Error processing reminders:', error);\r"}, {"line": 85, "type": "error", "content": "console.error('Error getting reminder stats:', error);", "fullLine": "    console.error('Error getting reminder stats:', error);\r"}], "count": 2}, {"path": "src\\app\\api\\notifications\\route.ts", "statements": [{"line": 138, "type": "error", "content": "console.error('Database error in notifications fetch:', dbError);", "fullLine": "      console.error('Database error in notifications fetch:', dbError);\r"}, {"line": 158, "type": "error", "content": "console.error('Unexpected error in notifications fetch:', error);", "fullLine": "    console.error('Unexpected error in notifications fetch:', error);\r"}, {"line": 238, "type": "error", "content": "console.error('Error creating notification:', error);", "fullLine": "    console.error('Error creating notification:', error);\r"}], "count": 3}, {"path": "src\\app\\api\\notifications\\sse\\route.ts", "statements": [{"line": 42, "type": "log", "content": "console.log('Keep-alive failed, connection closed:', connectionId);", "fullLine": "            console.log('Keep-alive failed, connection closed:', connectionId);\r"}, {"line": 52, "type": "log", "content": "console.log('SSE connection closed:', connectionId);", "fullLine": "          console.log('SSE connection closed:', connectionId);\r"}, {"line": 57, "type": "log", "content": "console.log('SSE connection cancelled:', connectionId);", "fullLine": "        console.log('SSE connection cancelled:', connectionId);\r"}, {"line": 72, "type": "error", "content": "console.error('SSE endpoint error:', error);", "fullLine": "    console.error('SSE endpoint error:', error);\r"}, {"line": 96, "type": "log", "content": "console.log('Failed to send notification via SSE, removing connection:', connId);", "fullLine": "      console.log('Failed to send notification via SSE, removing connection:', connId);\r"}, {"line": 114, "type": "log", "content": "console.log('Failed to send system notification via SSE, removing connection:', connId);", "fullLine": "      console.log('Failed to send system notification via SSE, removing connection:', connId);\r"}], "count": 6}, {"path": "src\\app\\api\\notifications\\system\\route.ts", "statements": [{"line": 67, "type": "error", "content": "console.error('Error creating system notification:', error);", "fullLine": "    console.error('Error creating system notification:', error);\r"}, {"line": 126, "type": "error", "content": "console.error('Error getting system notification templates:', error);", "fullLine": "    console.error('Error getting system notification templates:', error);\r"}], "count": 2}, {"path": "src\\app\\api\\notifications\\[id]\\route.ts", "statements": [{"line": 51, "type": "warn", "content": "console.warn('Could not describe notifications table:', describeError);", "fullLine": "      console.warn('Could not describe notifications table:', describeError);\r"}, {"line": 102, "type": "error", "content": "console.error('Error deleting notification:', error);", "fullLine": "    console.error('Error deleting notification:', error);\r"}], "count": 2}, {"path": "src\\app\\api\\packages\\route.ts", "statements": [{"line": 76, "type": "error", "content": "console.error(err);", "fullLine": "    console.error(err);\r"}, {"line": 272, "type": "error", "content": "console.error('Failed to process images for package:', result.packageId, imageError);", "fullLine": "        console.error('Failed to process images for package:', result.packageId, imageError);\r"}, {"line": 311, "type": "error", "content": "console.error('Failed to process custom options for package:', result.packageId, customOptionsError);", "fullLine": "        console.error('Failed to process custom options for package:', result.packageId, customOptionsError);\r"}, {"line": 361, "type": "error", "content": "console.error('Failed to process pet types for package:', result.packageId, petTypesError);", "fullLine": "        console.error('Failed to process pet types for package:', result.packageId, petTypesError);\r"}, {"line": 372, "type": "error", "content": "console.error(err);", "fullLine": "    console.error(err);\r"}, {"line": 409, "type": "error", "content": "console.error(err);", "fullLine": "    console.error(err);\r"}], "count": 6}, {"path": "src\\app\\api\\packages\\[id]\\images\\route.ts", "statements": [{"line": 44, "type": "error", "content": "console.error('Error parsing request body:', parseError);", "fullLine": "      console.error('Error parsing request body:', parseError);\r"}, {"line": 125, "type": "error", "content": "console.error('Error deleting file:', fileError);", "fullLine": "      console.error('Error deleting file:', fileError);\r"}, {"line": 135, "type": "error", "content": "console.error('=== ERROR IN DELETE IMAGE API ===');", "fullLine": "    console.error('=== ERROR IN DELETE IMAGE API ===');\r"}, {"line": 136, "type": "error", "content": "console.error('Error deleting package image:', error);", "fullLine": "    console.error('Error deleting package image:', error);\r"}], "count": 4}, {"path": "src\\app\\api\\packages\\[id]\\route.ts", "statements": [{"line": 203, "type": "error", "content": "console.error('Error toggling package status:', updateError);", "fullLine": "        console.error('Error toggling package status:', updateError);\r"}, {"line": 217, "type": "error", "content": "console.error('Missing required fields:', { name: body.name, description: body.description, price: body.price });", "fullLine": "          console.error('Missing required fields:', { name: body.name, description: body.description, price: body.price });\r"}, {"line": 343, "type": "error", "content": "console.error('Error deleting unused file:', fileError);", "fullLine": "            console.error('Error deleting unused file:', fileError);\r"}, {"line": 370, "type": "error", "content": "console.error('Package update error:', error);", "fullLine": "      console.error('Package update error:', error);\r"}, {"line": 377, "type": "error", "content": "console.error('Unexpected error in PATCH /api/packages/[id]:', unexpectedError);", "fullLine": "    console.error('Unexpected error in PATCH /api/packages/[id]:', unexpectedError);\r"}], "count": 5}, {"path": "src\\app\\api\\payments\\cleanup\\route.ts", "statements": [{"line": 41, "type": "error", "content": "console.error('Payment cleanup error:', error);", "fullLine": "    console.error('Payment cleanup error:', error);\r"}, {"line": 198, "type": "error", "content": "console.error('Payment status check error:', error);", "fullLine": "    console.error('Payment status check error:', error);\r"}], "count": 2}, {"path": "src\\app\\api\\payments\\create-intent\\route.ts", "statements": [{"line": 83, "type": "log", "content": "console.log('Payment request created:', {", "fullLine": "    console.log('Payment request created:', {\r"}, {"line": 114, "type": "error", "content": "console.error('Payment intent creation error:', error);", "fullLine": "    console.error('Payment intent creation error:', error);\r"}, {"line": 157, "type": "error", "content": "console.error('Payment status retrieval error:', error);", "fullLine": "    console.error('Payment status retrieval error:', error);\r"}], "count": 3}, {"path": "src\\app\\api\\payments\\status\\route.ts", "statements": [{"line": 117, "type": "error", "content": "console.error('Error fetching latest status from PayMongo:', providerError);", "fullLine": "        console.error('Error fetching latest status from PayMongo:', providerError);\r"}, {"line": 145, "type": "error", "content": "console.error('Error retrieving payment status:', error);", "fullLine": "    console.error('Error retrieving payment status:', error);\r"}], "count": 2}, {"path": "src\\app\\api\\payments\\webhook\\route.ts", "statements": [{"line": 17, "type": "error", "content": "console.error('Webhook signature validation failed');", "fullLine": "      console.error('Webhook signature validation failed');\r"}, {"line": 26, "type": "error", "content": "console.error('Invalid JSON in webhook payload:', error);", "fullLine": "      console.error('Invalid JSON in webhook payload:', error);\r"}, {"line": 32, "type": "error", "content": "console.error('Invalid webhook structure:', webhookData);", "fullLine": "      console.error('Invalid webhook structure:', webhookData);\r"}, {"line": 40, "type": "log", "content": "console.log('Received webhook:', {", "fullLine": "    console.log('Received webhook:', {\r"}, {"line": 74, "type": "error", "content": "console.error('Webhook processing error:', error);", "fullLine": "    console.error('Webhook processing error:', error);\r"}, {"line": 109, "type": "error", "content": "console.error('Error handling source.chargeable:', error);", "fullLine": "    console.error('Error handling source.chargeable:', error);\r"}, {"line": 149, "type": "error", "content": "console.error('Error handling payment.paid:', error);", "fullLine": "    console.error('Error handling payment.paid:', error);\r"}, {"line": 183, "type": "error", "content": "console.error('Error handling payment.failed:', error);", "fullLine": "    console.error('Error handling payment.failed:', error);\r"}, {"line": 226, "type": "error", "content": "console.error('Failed to create user notification:', notificationError);", "fullLine": "        console.error('Failed to create user notification:', notificationError);\r"}, {"line": 258, "type": "error", "content": "console.error('Failed to create provider notification:', providerNotificationError);", "fullLine": "          console.error('Failed to create provider notification:', providerNotificationError);\r"}, {"line": 263, "type": "warn", "content": "console.warn('Refund record not found for PayMongo refund:', refundId);", "fullLine": "      console.warn('Refund record not found for PayMongo refund:', refundId);\r"}, {"line": 266, "type": "error", "content": "console.error('Error handling refund.succeeded:', error);", "fullLine": "    console.error('Error handling refund.succeeded:', error);\r"}, {"line": 300, "type": "warn", "content": "console.warn('Refund record not found for PayMongo refund:', refundId);", "fullLine": "      console.warn('Refund record not found for PayMongo refund:', refundId);\r"}, {"line": 303, "type": "error", "content": "console.error('Error handling refund.failed:', error);", "fullLine": "    console.error('Error handling refund.failed:', error);\r"}], "count": 14}, {"path": "src\\app\\api\\pets\\route.ts", "statements": [{"line": 88, "type": "error", "content": "console.error('Database error fetching pets:', dbError);", "fullLine": "      console.error('Database error fetching pets:', dbError);\r"}, {"line": 95, "type": "error", "content": "console.error('General error in pets GET route:', error);", "fullLine": "    console.error('General error in pets GET route:', error);\r"}], "count": 2}, {"path": "src\\app\\api\\reviews\\booking\\[id]\\route.ts", "statements": [{"line": 37, "type": "error", "content": "console.error('Error checking booking review:', error);", "fullLine": "    console.error('Error checking booking review:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\reviews\\pending\\route.ts", "statements": [{"line": 56, "type": "error", "content": "console.error('Error checking/creating reviews table:', error);", "fullLine": "      console.error('Error checking/creating reviews table:', error);\r"}, {"line": 138, "type": "error", "content": "console.error('Error with business_services query:', error);", "fullLine": "          console.error('Error with business_services query:', error);\r"}, {"line": 205, "type": "error", "content": "console.error('Error with direct query:', error);", "fullLine": "          console.error('Error with direct query:', error);\r"}, {"line": 262, "type": "error", "content": "console.error('Error with fallback query:', error);", "fullLine": "          console.error('Error with fallback query:', error);\r"}, {"line": 266, "type": "error", "content": "console.error('Error checking tables:', error);", "fullLine": "      console.error('Error checking tables:', error);\r"}, {"line": 278, "type": "error", "content": "console.error('Error in pending reviews API:', error);", "fullLine": "    console.error('Error in pending reviews API:', error);\r"}], "count": 6}, {"path": "src\\app\\api\\reviews\\provider\\[id]\\route.ts", "statements": [{"line": 101, "type": "log", "content": "console.log('First review sample:', {", "fullLine": "        console.log('First review sample:', {\r"}, {"line": 110, "type": "error", "content": "console.error('Error with dynamic JOIN query:', joinError);", "fullLine": "      console.error('Error with dynamic JOIN query:', joinError);\r"}, {"line": 134, "type": "log", "content": "console.log('First review sample from simplified query:', {", "fullLine": "          console.log('First review sample from simplified query:', {\r"}, {"line": 175, "type": "error", "content": "console.error('Error with simplified JOIN query:', simpleError);", "fullLine": "        console.error('Error with simplified JOIN query:', simpleError);\r"}, {"line": 221, "type": "error", "content": "console.error('Error with basic query:', basicError);", "fullLine": "          console.error('Error with basic query:', basicError);\r"}, {"line": 230, "type": "error", "content": "console.error('Final query attempt failed:', finalError);", "fullLine": "            console.error('Final query attempt failed:', finalError);\r"}, {"line": 266, "type": "error", "content": "console.error('Error fetching provider reviews:', error);", "fullLine": "    console.error('Error fetching provider reviews:', error);\r"}], "count": 7}, {"path": "src\\app\\api\\reviews\\route.ts", "statements": [{"line": 28, "type": "error", "content": "console.error('Error decoding JWT token:', error);", "fullLine": "        console.error('Error decoding JWT token:', error);\r"}, {"line": 47, "type": "log", "content": "console.log('Review submission data:', {", "fullLine": "    console.log('Review submission data:', {\r"}, {"line": 58, "type": "error", "content": "console.error('Missing required fields:', {", "fullLine": "      console.error('Missing required fields:', {\r"}, {"line": 78, "type": "error", "content": "console.error('User ID mismatch:', { tokenUserId, requestUserId: user_id });", "fullLine": "      console.error('User ID mismatch:', { tokenUserId, requestUserId: user_id });\r"}, {"line": 145, "type": "error", "content": "console.error('Error checking tables:', error);", "fullLine": "      console.error('Error checking tables:', error);\r"}, {"line": 179, "type": "error", "content": "console.error('Error creating reviews table:', error);", "fullLine": "      console.error('Error creating reviews table:', error);\r"}, {"line": 202, "type": "error", "content": "console.error('Error checking for expiration_date column:', error);", "fullLine": "      console.error('Error checking for expiration_date column:', error);\r"}, {"line": 295, "type": "error", "content": "console.error('Error creating notification:', notificationError);", "fullLine": "      console.error('Error creating notification:', notificationError);\r"}, {"line": 304, "type": "error", "content": "console.error('Error creating review:', error);", "fullLine": "    console.error('Error creating review:', error);\r"}], "count": 9}, {"path": "src\\app\\api\\reviews\\user\\[id]\\route.ts", "statements": [{"line": 42, "type": "error", "content": "console.error('Error fetching user reviews:', error);", "fullLine": "    console.error('Error fetching user reviews:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\reviews\\user-booking\\[bookingId]\\route.ts", "statements": [{"line": 48, "type": "error", "content": "console.error('Error decoding JWT token:', error);", "fullLine": "        console.error('Error decoding JWT token:', error);\r"}, {"line": 89, "type": "error", "content": "console.error('Error fetching review:', error);", "fullLine": "    console.error('Error fetching review:', error);\r"}], "count": 2}, {"path": "src\\app\\api\\service-providers\\route.ts", "statements": [{"line": 34, "type": "warn", "content": "console.warn('⚠️ [API] Invalid coordinates provided, falling back to geocoding');", "fullLine": "      console.warn('⚠️ [API] Invalid coordinates provided, falling back to geocoding');\r"}, {"line": 189, "type": "warn", "content": "console.warn('📍 [Distance] Provider coordinates are null, skipping distance calculation for provider:', provider.id);", "fullLine": "            console.warn('📍 [Distance] Provider coordinates are null, skipping distance calculation for provider:', provider.id);\r"}, {"line": 205, "type": "log", "content": "console.log(`📍 [Server Cache Hit] Provider ${provider.id}: ${cachedRoute.distance} (${cachedRoute.provider})`);", "fullLine": "            console.log(`📍 [Server Cache Hit] Provider ${provider.id}: ${cachedRoute.distance} (${cachedRoute.provider})`);\r"}, {"line": 245, "type": "log", "content": "console.log(`📍 [Routing] Provider ${provider.id}: ${routeResult.distance} (${routeResult.provider})`);", "fullLine": "            console.log(`📍 [Routing] Provider ${provider.id}: ${routeResult.distance} (${routeResult.provider})`);\r"}, {"line": 247, "type": "warn", "content": "console.warn(`📍 [Routing] Failed for provider ${provider.id}, falling back to straight-line distance:`, routingError);", "fullLine": "            console.warn(`📍 [Routing] Failed for provider ${provider.id}, falling back to straight-line distance:`, routingError);\r"}, {"line": 293, "type": "error", "content": "console.error('📍 [Provider Processing] Failed to process provider:', provider.id, error);", "fullLine": "            console.error('📍 [Provider Processing] Failed to process provider:', provider.id, error);\r"}, {"line": 320, "type": "error", "content": "console.error(`📍 [Provider Processing] Failed to process provider ${providersResult[index]?.id}:`, result.reason);", "fullLine": "              console.error(`📍 [Provider Processing] Failed to process provider ${providersResult[index]?.id}:`, result.reason);\r"}, {"line": 337, "type": "error", "content": "console.error('Database error in service-providers:', dbError);", "fullLine": "      console.error('Database error in service-providers:', dbError);\r"}, {"line": 341, "type": "error", "content": "console.error('General error in service-providers:', error);", "fullLine": "    console.error('General error in service-providers:', error);\r"}], "count": 9}, {"path": "src\\app\\api\\service-providers\\[id]\\route.ts", "statements": [{"line": 114, "type": "error", "content": "console.error('Error fetching provider rating:', ratingError);", "fullLine": "        console.error('Error fetching provider rating:', ratingError);\r"}, {"line": 130, "type": "warn", "content": "console.warn('📍 [Distance] Provider coordinates are null for provider:', provider.id);", "fullLine": "            console.warn('📍 [Distance] Provider coordinates are null for provider:', provider.id);\r"}, {"line": 145, "type": "log", "content": "console.log(`📍 [Server Cache Hit] Provider ${provider.id}: ${cachedRoute.distance} (${cachedRoute.provider})`);", "fullLine": "                console.log(`📍 [Server Cache Hit] Provider ${provider.id}: ${cachedRoute.distance} (${cachedRoute.provider})`);\r"}, {"line": 192, "type": "log", "content": "console.log(`📍 [Routing] Provider ${provider.id}: ${routeResult.distance} (${routeResult.provider})`);", "fullLine": "                  console.log(`📍 [Routing] Provider ${provider.id}: ${routeResult.distance} (${routeResult.provider})`);\r"}, {"line": 195, "type": "warn", "content": "console.warn(`📍 [Routing] Failed for provider ${provider.id} (${errorMessage}), falling back to straight-line distance`);", "fullLine": "                  console.warn(`📍 [Routing] Failed for provider ${provider.id} (${errorMessage}), falling back to straight-line distance`);\r"}, {"line": 203, "type": "error", "content": "console.error('Distance calculation failed:', error);", "fullLine": "              console.error('Distance calculation failed:', error);\r"}], "count": 6}, {"path": "src\\app\\api\\user\\notifications\\route.ts", "statements": [{"line": 36, "type": "error", "content": "console.error('Fetch notifications error:', error);", "fullLine": "    console.error('Fetch notifications error:', error);\r"}, {"line": 100, "type": "error", "content": "console.error('Mark notification as read error:', error);", "fullLine": "    console.error('Mark notification as read error:', error);\r"}], "count": 2}, {"path": "src\\app\\api\\user\\notifications\\[id]\\route.ts", "statements": [{"line": 71, "type": "warn", "content": "console.warn('Could not describe notifications table:', describeError);", "fullLine": "      console.warn('Could not describe notifications table:', describeError);\r"}, {"line": 101, "type": "error", "content": "console.error('Delete user notification error:', error);", "fullLine": "    console.error('Delete user notification error:', error);\r"}], "count": 2}, {"path": "src\\app\\api\\users\\notification-preferences\\route.ts", "statements": [{"line": 77, "type": "error", "content": "console.error('Error fetching notification preferences:', error);", "fullLine": "    console.error('Error fetching notification preferences:', error);\r"}, {"line": 127, "type": "error", "content": "console.error('Error updating notification preferences:', error);", "fullLine": "    console.error('Error updating notification preferences:', error);\r"}, {"line": 166, "type": "error", "content": "console.error('Error ensuring notification columns exist:', error);", "fullLine": "    console.error('Error ensuring notification columns exist:', error);\r"}], "count": 3}, {"path": "src\\app\\api\\users\\route.ts", "statements": [{"line": 172, "type": "error", "content": "console.error('Error fetching user appeals:', error);", "fullLine": "        console.error('Error fetching user appeals:', error);\r"}, {"line": 242, "type": "error", "content": "console.error(`Error getting pet count for user ${user.user_id}:`, error);", "fullLine": "                console.error(`Error getting pet count for user ${user.user_id}:`, error);\r"}, {"line": 260, "type": "error", "content": "console.error(`Error getting completed bookings for user ${user.user_id}:`, error);", "fullLine": "                console.error(`Error getting completed bookings for user ${user.user_id}:`, error);\r"}], "count": 3}, {"path": "src\\app\\api\\users\\upload-profile-picture\\route.ts", "statements": [{"line": 40, "type": "error", "content": "console.error(`Failed to save profile picture:`, error);", "fullLine": "    console.error(`Failed to save profile picture:`, error);\r"}, {"line": 111, "type": "error", "content": "console.error('Error cleaning up old profile pictures:', cleanupError);", "fullLine": "      console.error('Error cleaning up old profile pictures:', cleanupError);\r"}, {"line": 150, "type": "error", "content": "console.error('Database error while updating profile picture:', dbError);", "fullLine": "      console.error('Database error while updating profile picture:', dbError);\r"}, {"line": 158, "type": "error", "content": "console.error('Profile picture upload error:', error);", "fullLine": "    console.error('Profile picture upload error:', error);\r"}], "count": 4}, {"path": "src\\app\\api\\users\\[id]\\profile-picture\\route.ts", "statements": [{"line": 40, "type": "error", "content": "console.error(`Failed to save profile picture:`, error);", "fullLine": "    console.error(`Failed to save profile picture:`, error);\r"}, {"line": 115, "type": "error", "content": "console.error('Error cleaning up old profile pictures:', cleanupError);", "fullLine": "      console.error('Error cleaning up old profile pictures:', cleanupError);\r"}, {"line": 154, "type": "error", "content": "console.error('Database error while updating profile picture:', dbError);", "fullLine": "      console.error('Database error while updating profile picture:', dbError);\r"}, {"line": 162, "type": "error", "content": "console.error('Error in profile picture upload:', error);", "fullLine": "    console.error('Error in profile picture upload:', error);\r"}], "count": 4}, {"path": "src\\app\\api\\users\\[id]\\restrict\\route.ts", "statements": [{"line": 124, "type": "error", "content": "console.error('Failed to send restriction notification:', error);", "fullLine": "          console.error('Failed to send restriction notification:', error);\r"}, {"line": 194, "type": "error", "content": "console.error('User restriction error:', error);", "fullLine": "    console.error('User restriction error:', error);\r"}, {"line": 219, "type": "error", "content": "console.error('Failed to create restriction notification:', notificationResult.error);", "fullLine": "      console.error('Failed to create restriction notification:', notificationResult.error);\r"}, {"line": 244, "type": "error", "content": "console.error('Error notifying user of restriction:', error);", "fullLine": "    console.error('Error notifying user of restriction:', error);\r"}], "count": 4}, {"path": "src\\app\\api\\users\\[id]\\role\\route.ts", "statements": [{"line": 137, "type": "error", "content": "console.error('User role update error:', error);", "fullLine": "    console.error('User role update error:', error);\r"}], "count": 1}, {"path": "src\\app\\api\\users\\[id]\\route.ts", "statements": [{"line": 68, "type": "error", "content": "console.error(`[User API] Error getting pet count:`, error);", "fullLine": "              console.error(`[User API] Error getting pet count:`, error);\r"}, {"line": 94, "type": "error", "content": "console.error(`[User API] Error getting completed bookings:`, error);", "fullLine": "              console.error(`[User API] Error getting completed bookings:`, error);\r"}, {"line": 134, "type": "error", "content": "console.error(`[User API] Error fetching business details:`, businessError);", "fullLine": "            console.error(`[User API] Error fetching business details:`, businessError);\r"}, {"line": 146, "type": "error", "content": "console.error(`[User API] Database error:`, dbError);", "fullLine": "      console.error(`[User API] Database error:`, dbError);\r"}, {"line": 206, "type": "error", "content": "console.error(`[User API] Admin database error:`, adminDbError);", "fullLine": "      console.error(`[User API] Admin database error:`, adminDbError);\r"}, {"line": 215, "type": "error", "content": "console.error(`[User API] Unexpected error:`, error);", "fullLine": "    console.error(`[User API] Unexpected error:`, error);\r"}], "count": 6}, {"path": "src\\app\\appeals\\page.tsx", "statements": [{"line": 139, "type": "error", "content": "console.error('Error checking user status:', error);", "fullLine": "      console.error('Error checking user status:', error);\r"}, {"line": 160, "type": "error", "content": "console.error('Error loading appeals:', error);", "fullLine": "      console.error('Error loading appeals:', error);\r"}, {"line": 172, "type": "error", "content": "console.error('Error loading appeal history:', error);", "fullLine": "      console.error('Error loading appeal history:', error);\r"}, {"line": 206, "type": "error", "content": "console.error('<PERSON><PERSON><PERSON> submitting appeal:', error);", "fullLine": "      console.error('<PERSON><PERSON><PERSON> submitting appeal:', error);\r"}], "count": 4}, {"path": "src\\app\\cremation\\pending-verification\\page.tsx", "statements": [{"line": 71, "type": "error", "content": "console.error('Error checking status:', error);", "fullLine": "        console.error('Error checking status:', error);\r"}], "count": 1}, {"path": "src\\app\\cremation\\profile\\page.tsx", "statements": [{"line": 134, "type": "log", "content": "console.log('No authentication found, skipping profile data fetch');", "fullLine": "      console.log('No authentication found, skipping profile data fetch');\r"}, {"line": 234, "type": "log", "content": "console.log('Profile data fetch was aborted');", "fullLine": "        console.log('Profile data fetch was aborted');\r"}, {"line": 243, "type": "error", "content": "console.error('Error fetching profile data:', error);", "fullLine": "      console.error('Error fetching profile data:', error);\r"}, {"line": 273, "type": "log", "content": "console.log('Logout scenario detected, suppressing error handling');", "fullLine": "        console.log('Logout scenario detected, suppressing error handling');\r"}, {"line": 435, "type": "error", "content": "console.error('Address update error:', error);", "fullLine": "      console.error('Address update error:', error);\r"}, {"line": 468, "type": "error", "content": "console.error('Error updating contact information:', error);", "fullLine": "      console.error('Error updating contact information:', error);\r"}, {"line": 539, "type": "warn", "content": "console.warn('Nominatim rate limited, using fallback geocoding');", "fullLine": "          console.warn('Nominatim rate limited, using fallback geocoding');\r"}, {"line": 559, "type": "warn", "content": "console.warn('Nominatim service unavailable, using fallback geocoding');", "fullLine": "        console.warn('Nominatim service unavailable, using fallback geocoding');\r"}, {"line": 589, "type": "warn", "content": "console.warn('Reverse geocoding failed:', geocodeError.message);", "fullLine": "        console.warn('Reverse geocoding failed:', geocodeError.message);\r"}, {"line": 653, "type": "error", "content": "console.error('Error updating business information:', error);", "fullLine": "      console.error('Error updating business information:', error);\r"}, {"line": 706, "type": "error", "content": "console.error('FileReader error:', error);", "fullLine": "        console.error('FileReader error:', error);\r"}], "count": 11}, {"path": "src\\app\\cremation\\restricted\\page.tsx", "statements": [{"line": 43, "type": "error", "content": "console.error('Error checking status:', error);", "fullLine": "        console.error('Error checking status:', error);\r"}], "count": 1}, {"path": "src\\app\\cremation\\reviews\\page.tsx", "statements": [{"line": 74, "type": "log", "content": "console.log(`Fetching reviews for provider ID: ${effectiveProviderId} (from userData:`, {", "fullLine": "        console.log(`Fetching reviews for provider ID: ${effectiveProviderId} (from userData:`, {\r"}, {"line": 98, "type": "log", "content": "console.log('First review sample:', {", "fullLine": "            console.log('First review sample:', {\r"}, {"line": 124, "type": "warn", "content": "console.warn('No reviews array in API response:', data);", "fullLine": "          console.warn('No reviews array in API response:', data);\r"}, {"line": 149, "type": "error", "content": "console.error('Error fetching reviews:', error);", "fullLine": "          console.error('Error fetching reviews:', error);\r"}], "count": 4}, {"path": "src\\app\\cremation\\settings\\page.tsx", "statements": [{"line": 57, "type": "error", "content": "console.error('Failed to load notification settings:', error);", "fullLine": "        console.error('Failed to load notification settings:', error);\r"}], "count": 1}, {"path": "src\\app\\payment\\failed\\page.tsx", "statements": [{"line": 24, "type": "error", "content": "console.error('Failed to get payment details:', data.error);", "fullLine": "        console.error('Failed to get payment details:', data.error);\r"}, {"line": 27, "type": "error", "content": "console.error('Error getting payment details:', error);", "fullLine": "      console.error('Error getting payment details:', error);\r"}], "count": 2}, {"path": "src\\app\\payment\\success\\page.tsx", "statements": [{"line": 39, "type": "error", "content": "console.error('Error verifying payment:', error);", "fullLine": "      console.error('Error verifying payment:', error);\r"}], "count": 1}, {"path": "src\\app\\restricted\\page.tsx", "statements": [{"line": 66, "type": "error", "content": "console.error('Error checking status:', error);", "fullLine": "        console.error('Error checking status:', error);\r"}], "count": 1}, {"path": "src\\app\\user\\furparent_dashboard\\bookings\\checkout\\page.tsx", "statements": [{"line": 75, "type": "error", "content": "console.error('❌ [Checkout] Error parsing session user data:', error);", "fullLine": "          console.error('❌ [Checkout] Error parsing session user data:', error);\r"}, {"line": 421, "type": "error", "content": "console.error(`Provider API failed: ${providerResponse.status} for provider ID: ${providerIdParam}`);", "fullLine": "          console.error(`Provider API failed: ${providerResponse.status} for provider ID: ${providerIdParam}`);\r"}, {"line": 537, "type": "error", "content": "console.error('❌ [Checkout] Failed to fetch user data from API');", "fullLine": "              console.error('❌ [Checkout] Failed to fetch user data from API');\r"}, {"line": 541, "type": "error", "content": "console.error('❌ [Checkout] Error fetching user data:', error);", "fullLine": "          console.error('❌ [Checkout] Error fetching user data:', error);\r"}, {"line": 572, "type": "warn", "content": "console.warn('❌ [Checkout] Missing coordinates - delivery:', deliveryCoordinates, 'provider:', providerCoordinates);", "fullLine": "            console.warn('❌ [Checkout] Missing coordinates - delivery:', deliveryCoordinates, 'provider:', providerCoordinates);\r"}, {"line": 590, "type": "error", "content": "console.error('❌ [Checkout] Error calculating delivery distance:', error);", "fullLine": "          console.error('❌ [Checkout] Error calculating delivery distance:', error);\r"}, {"line": 861, "type": "error", "content": "console.error('Payment creation error:', paymentError);", "fullLine": "          console.error('Payment creation error:', paymentError);\r"}], "count": 7}, {"path": "src\\app\\user\\furparent_dashboard\\bookings\\page.tsx", "statements": [{"line": 101, "type": "error", "content": "console.error('Error parsing cached user data:', error);", "fullLine": "          console.error('Error parsing cached user data:', error);\r"}, {"line": 147, "type": "warn", "content": "console.warn('Booking not found:', bookingIdNum);", "fullLine": "            console.warn('Booking not found:', bookingIdNum);\r"}, {"line": 290, "type": "error", "content": "console.error('Error checking reviewed bookings:', error);", "fullLine": "      console.error('Error checking reviewed bookings:', error);\r"}, {"line": 508, "type": "error", "content": "console.error('Invalid date components:', { year, month, day });", "fullLine": "        console.error('Invalid date components:', { year, month, day });\r"}, {"line": 523, "type": "error", "content": "console.error('Invalid date object created:', dateObj);", "fullLine": "        console.error('Invalid date object created:', dateObj);\r"}, {"line": 532, "type": "error", "content": "console.error('Date formatting error:', error, { date, time });", "fullLine": "      console.error('Date formatting error:', error, { date, time });\r"}, {"line": 544, "type": "error", "content": "console.error('Fallback date parsing failed:', fallbackError);", "fullLine": "        console.error('Fallback date parsing failed:', fallbackError);\r"}], "count": 7}, {"path": "src\\app\\user\\furparent_dashboard\\cart\\page.tsx", "statements": [{"line": 36, "type": "error", "content": "console.error('Cart item missing provider or package ID:', item);", "fullLine": "      console.error('Cart item missing provider or package ID:', item);\r"}], "count": 1}, {"path": "src\\app\\user\\furparent_dashboard\\profile\\page.tsx", "statements": [{"line": 89, "type": "error", "content": "console.error('Failed to update session storage:', error);", "fullLine": "          console.error('Failed to update session storage:', error);\r"}, {"line": 292, "type": "error", "content": "console.error('Error uploading profile picture:', error);", "fullLine": "      console.error('Error uploading profile picture:', error);\r"}, {"line": 340, "type": "error", "content": "console.error('Error updating personal info:', error);", "fullLine": "      console.error('Error updating personal info:', error);\r"}, {"line": 392, "type": "error", "content": "console.error('Error updating contact info:', error);", "fullLine": "      console.error('Error updating contact info:', error);\r"}, {"line": 460, "type": "warn", "content": "console.warn('Nominatim rate limited, using fallback geocoding');", "fullLine": "          console.warn('Nominatim rate limited, using fallback geocoding');\r"}, {"line": 480, "type": "warn", "content": "console.warn('Nominatim service unavailable, using fallback geocoding');", "fullLine": "        console.warn('Nominatim service unavailable, using fallback geocoding');\r"}, {"line": 518, "type": "warn", "content": "console.warn('Reverse geocoding failed:', geocodeError.message);", "fullLine": "        console.warn('Reverse geocoding failed:', geocodeError.message);\r"}, {"line": 601, "type": "warn", "content": "console.warn('Profile picture failed to load:', userData.profile_picture);", "fullLine": "                          console.warn('Profile picture failed to load:', userData.profile_picture);\r"}], "count": 8}, {"path": "src\\app\\user\\furparent_dashboard\\services\\page.tsx", "statements": [{"line": 40, "type": "error", "content": "console.error('Failed to parse user data from session storage:', error);", "fullLine": "            console.error('Failed to parse user data from session storage:', error);\r"}, {"line": 55, "type": "log", "content": "console.log('✅ Setting location to:', location);", "fullLine": "        console.log('✅ Setting location to:', location);\r"}, {"line": 57, "type": "log", "content": "console.log('❌ No address found in user data');", "fullLine": "        console.log('❌ No address found in user data');\r"}, {"line": 79, "type": "error", "content": "console.error('Failed to update session storage:', error);", "fullLine": "          console.error('Failed to update session storage:', error);\r"}], "count": 4}, {"path": "src\\app\\user\\furparent_dashboard\\services\\[id]\\page.tsx", "statements": [{"line": 90, "type": "error", "content": "console.error('Failed to parse user data from session storage:', error);", "fullLine": "            console.error('Failed to parse user data from session storage:', error);\r"}, {"line": 118, "type": "error", "content": "console.error('Failed to update session storage:', error);", "fullLine": "          console.error('Failed to update session storage:', error);\r"}], "count": 2}, {"path": "src\\app\\user\\furparent_dashboard\\settings\\page.tsx", "statements": [{"line": 46, "type": "error", "content": "console.error('Failed to load notification settings:', error);", "fullLine": "        console.error('Failed to load notification settings:', error);\r"}], "count": 1}, {"path": "src\\components\\admin\\LogAnalytics.tsx", "statements": [{"line": 58, "type": "error", "content": "console.error('Error fetching log stats:', err);", "fullLine": "      console.error('Error fetching log stats:', err);\r"}], "count": 1}, {"path": "src\\components\\booking\\AddOnSelector.tsx", "statements": [{"line": 65, "type": "warn", "content": "console.warn('Add-ons loading error:', error);", "fullLine": "        console.warn('Add-ons loading error:', error);\r"}], "count": 1}, {"path": "src\\components\\booking\\AvailabilityCalendar.tsx", "statements": [{"line": 268, "type": "error", "content": "console.error('Error fetching bookings:', bookingError);", "fullLine": "        console.error('Error fetching bookings:', bookingError);\r"}, {"line": 358, "type": "error", "content": "console.error('Error fetching availability data:', err);", "fullLine": "      console.error('Error fetching availability data:', err);\r"}, {"line": 374, "type": "error", "content": "console.error('Error parsing cached data:', parseError);", "fullLine": "              console.error('Error parsing cached data:', parseError);\r"}, {"line": 1224, "type": "error", "content": "console.error('Error removing time slot:', error);", "fullLine": "      console.error('Error removing time slot:', error);\r"}, {"line": 1251, "type": "error", "content": "console.error('Error clearing date slots:', clearErr);", "fullLine": "        console.error('Error clearing date slots:', clearErr);\r"}], "count": 5}, {"path": "src\\components\\booking\\TimeSlotSelector.tsx", "statements": [{"line": 75, "type": "error", "content": "console.error(`TimeSlotSelector: API response not OK: ${response.status} ${response.statusText}`);", "fullLine": "        console.error(`TimeSlotSelector: API response not OK: ${response.status} ${response.statusText}`);\r"}, {"line": 95, "type": "error", "content": "console.error('TimeSlotSelector: Error fetching availability data:', error);", "fullLine": "      console.error('TimeSlotSelector: Error fetching availability data:', error);\r"}, {"line": 260, "type": "error", "content": "console.error('TimeSlotSelector: Cannot select time slot without a date');", "fullLine": "      console.error('TimeSlotSelector: Cannot select time slot without a date');\r"}], "count": 3}, {"path": "src\\components\\cart\\CartSidebar.tsx", "statements": [{"line": 49, "type": "error", "content": "console.error('Cart item missing provider or package ID:', item);", "fullLine": "      console.error('Cart item missing provider or package ID:', item);\r"}], "count": 1}, {"path": "src\\components\\map\\MapComponent.tsx", "statements": [{"line": 164, "type": "error", "content": "console.error('🗺️ [MapComponent] Enhanced geocoding failed:', error);", "fullLine": "      console.error('🗺️ [MapComponent] Enhanced geocoding failed:', error);\r"}, {"line": 440, "type": "log", "content": "console.log('Route calculation already in progress, skipping...');", "fullLine": "      console.log('Route calculation already in progress, skipping...');\r"}, {"line": 467, "type": "log", "content": "console.log('Component unmounted or routing cancelled, aborting route display');", "fullLine": "        console.log('Component unmounted or routing cancelled, aborting route display');\r"}, {"line": 491, "type": "error", "content": "console.error('Enhanced routing failed:', error);", "fullLine": "      console.error('Enhanced routing failed:', error);\r"}], "count": 4}, {"path": "src\\components\\modals\\DocumentViewerModal.tsx", "statements": [{"line": 39, "type": "error", "content": "console.error('Document loading error:', {", "fullLine": "    console.error('Document loading error:', {\r"}], "count": 1}, {"path": "src\\components\\navigation\\CremationNavbar.tsx", "statements": [{"line": 77, "type": "error", "content": "console.error('Error reading cached profile picture:', error);", "fullLine": "        console.error('Error reading cached profile picture:', error);\r"}, {"line": 147, "type": "error", "content": "console.error('Failed to fetch profile picture:', error);", "fullLine": "      console.error('Failed to fetch profile picture:', error);\r"}, {"line": 220, "type": "error", "content": "console.error('Error updating caches:', error);", "fullLine": "          console.error('Error updating caches:', error);\r"}], "count": 3}, {"path": "src\\components\\navigation\\FurParentDashboardWrapper.tsx", "statements": [{"line": 62, "type": "error", "content": "console.error('Error creating review notifications:', error);", "fullLine": "        console.error('Error creating review notifications:', error);\r"}], "count": 1}, {"path": "src\\components\\navigation\\FurParentNavbar.tsx", "statements": [{"line": 75, "type": "error", "content": "console.error('Failed to parse user data during initialization:', error);", "fullLine": "        console.error('Failed to parse user data during initialization:', error);\r"}, {"line": 104, "type": "error", "content": "console.error('Failed to parse user data:', error);", "fullLine": "        console.error('Failed to parse user data:', error);\r"}, {"line": 119, "type": "error", "content": "console.error('Failed to parse user data for API call:', error);", "fullLine": "            console.error('Failed to parse user data for API call:', error);\r"}, {"line": 149, "type": "error", "content": "console.error('Failed to update user data cache:', error);", "fullLine": "                  console.error('Failed to update user data cache:', error);\r"}, {"line": 156, "type": "error", "content": "console.error('Failed to fetch user profile picture:', error);", "fullLine": "        console.error('Failed to fetch user profile picture:', error);\r"}, {"line": 186, "type": "error", "content": "console.error('Failed to parse user data:', error);", "fullLine": "          console.error('Failed to parse user data:', error);\r"}, {"line": 198, "type": "error", "content": "console.error('Failed to get localStorage backup:', error);", "fullLine": "          console.error('Failed to get localStorage backup:', error);\r"}, {"line": 405, "type": "warn", "content": "console.warn('Profile picture failed to load, using fallback');", "fullLine": "                        console.warn('Profile picture failed to load, using fallback');\r"}], "count": 8}, {"path": "src\\components\\packages\\ImageUploader.tsx", "statements": [{"line": 21, "type": "log", "content": "console.log('ImageUploader received images:', images);", "fullLine": "  console.log('ImageUploader received images:', images);\r"}, {"line": 28, "type": "log", "content": "console.log(`Image ${i + 1} URL:`, img);", "fullLine": "          console.log(`Image ${i + 1} URL:`, img);\r"}, {"line": 44, "type": "log", "content": "console.log(`Removing image at index ${i}:`, img);", "fullLine": "                  console.log(`Removing image at index ${i}:`, img);\r"}], "count": 3}, {"path": "src\\components\\packages\\PackageModal.tsx", "statements": [{"line": 244, "type": "log", "content": "console.log('Package images from API:', pkg.images);", "fullLine": "      console.log('Package images from API:', pkg.images);\r"}, {"line": 250, "type": "log", "content": "console.log('Processing image path:', imagePath);", "fullLine": "        console.log('Processing image path:', imagePath);\r"}, {"line": 254, "type": "log", "content": "console.log('Image is already API path:', imagePath);", "fullLine": "          console.log('Image is already API path:', imagePath);\r"}, {"line": 261, "type": "log", "content": "console.log('Converting uploads path to API path:', imagePath, '->', apiPath);", "fullLine": "          console.log('Converting uploads path to API path:', imagePath, '->', apiPath);\r"}, {"line": 265, "type": "log", "content": "console.log('Using image path as-is:', imagePath);", "fullLine": "        console.log('Using image path as-is:', imagePath);\r"}, {"line": 269, "type": "log", "content": "console.log('Processed images for form:', processedImages);", "fullLine": "      console.log('Processed images for form:', processedImages);\r"}, {"line": 292, "type": "error", "content": "console.error('Failed to load package:', error);", "fullLine": "      console.error('Failed to load package:', error);\r"}, {"line": 436, "type": "error", "content": "console.error('Image upload failed:', error);", "fullLine": "      console.error('Image upload failed:', error);\r"}, {"line": 455, "type": "log", "content": "console.log('Removing image at index:', index);", "fullLine": "    console.log('Removing image at index:', index);\r"}, {"line": 458, "type": "log", "content": "console.log('Current images before removal:', prev.images);", "fullLine": "      console.log('Current images before removal:', prev.images);\r"}, {"line": 460, "type": "log", "content": "console.log('New images after removal:', newImages);", "fullLine": "      console.log('New images after removal:', newImages);\r"}], "count": 11}, {"path": "src\\components\\refund\\RefundButton.tsx", "statements": [{"line": 56, "type": "error", "content": "console.error('Error checking refund eligibility:', error);", "fullLine": "      console.error('Error checking refund eligibility:', error);\r"}], "count": 1}, {"path": "src\\components\\refund\\RefundRequestModal.tsx", "statements": [{"line": 61, "type": "error", "content": "console.error('Error submitting refund request:', error);", "fullLine": "      console.error('Error submitting refund request:', error);\r"}], "count": 1}, {"path": "src\\components\\reviews\\ReviewDisplay.tsx", "statements": [{"line": 48, "type": "error", "content": "console.error('Error fetching review:', error);", "fullLine": "        console.error('Error fetching review:', error);\r"}], "count": 1}, {"path": "src\\components\\reviews\\ReviewForm.tsx", "statements": [{"line": 53, "type": "error", "content": "console.error('Missing required props:', { bookingId, userId, providerId });", "fullLine": "      console.error('Missing required props:', { bookingId, userId, providerId });\r"}, {"line": 61, "type": "log", "content": "console.log('Submitting review:', {", "fullLine": "      console.log('Submitting review:', {\r"}, {"line": 89, "type": "error", "content": "console.error('Validation failed:', responseData.validation);", "fullLine": "          console.error('Validation failed:', responseData.validation);\r"}, {"line": 117, "type": "error", "content": "console.error('<PERSON>rror submitting review:', error);", "fullLine": "      console.error('<PERSON>rror submitting review:', error);\r"}], "count": 4}, {"path": "src\\components\\reviews\\ReviewModal.tsx", "statements": [{"line": 52, "type": "error", "content": "console.error('Error checking review status:', error);", "fullLine": "      console.error('Error checking review status:', error);\r"}, {"line": 74, "type": "error", "content": "console.error('ReviewModal: Missing required props:', {", "fullLine": "    console.error('ReviewModal: Missing required props:', { \r"}], "count": 2}, {"path": "src\\components\\reviews\\ReviewsList.tsx", "statements": [{"line": 88, "type": "error", "content": "console.error('Error fetching reviews:', error);", "fullLine": "        console.error('Error fetching reviews:', error);\r"}], "count": 1}, {"path": "src\\hooks\\useOTPVerification.ts", "statements": [{"line": 39, "type": "error", "content": "console.error('Error getting stored cooldown end time:', error);", "fullLine": "        console.error('Error getting stored cooldown end time:', error);\r"}, {"line": 51, "type": "error", "content": "console.error('Error setting stored cooldown end time:', error);", "fullLine": "        console.error('Error setting stored cooldown end time:', error);\r"}, {"line": 61, "type": "error", "content": "console.error('Error clearing stored cooldown:', error);", "fullLine": "        console.error('Error clearing stored cooldown:', error);\r"}, {"line": 101, "type": "error", "content": "console.error('Error generating OTP:', error);", "fullLine": "        console.error('Error generating OTP:', error);\r"}, {"line": 150, "type": "error", "content": "console.error('Error verifying OTP:', error);", "fullLine": "        console.error('Error verifying OTP:', error);\r"}], "count": 5}, {"path": "src\\hooks\\usePackages.ts", "statements": [{"line": 179, "type": "error", "content": "console.error('Package toggle error:', error);", "fullLine": "      console.error('Package toggle error:', error);\r"}], "count": 1}, {"path": "src\\lib\\auth.ts", "statements": [{"line": 67, "type": "error", "content": "console.error('Error getting server session:', error);", "fullLine": "    console.error('Error getting server session:', error);\r"}], "count": 1}, {"path": "src\\lib\\consolidatedEmailService.ts", "statements": [{"line": 63, "type": "warn", "content": "console.warn('Email service not properly configured: Missing SMTP credentials');", "fullLine": "    console.warn('Email service not properly configured: Missing SMTP credentials');\r"}, {"line": 377, "type": "error", "content": "console.error('Failed to record email in log:', error);", "fullLine": "    console.error('Failed to record email in log:', error);\r"}], "count": 2}, {"path": "src\\lib\\db.ts", "statements": [{"line": 174, "type": "error", "content": "console.error(`Database query error (attempt ${attempt}/${maxRetries}):`, {", "fullLine": "      console.error(`Database query error (attempt ${attempt}/${maxRetries}):`, {\r"}, {"line": 190, "type": "warn", "content": "console.warn(`Retrying query in ${waitTime}ms due to ${err.code}`);", "fullLine": "        console.warn(`Retrying query in ${waitTime}ms due to ${err.code}`);\r"}, {"line": 211, "type": "error", "content": "console.error('MySQL server connection refused. Please ensure MySQL is running on port 3306.');", "fullLine": "    console.error('MySQL server connection refused. Please ensure MySQL is running on port 3306.');\r"}, {"line": 213, "type": "error", "content": "console.error('MySQL access denied. Please check database credentials.');", "fullLine": "    console.error('MySQL access denied. Please check database credentials.');\r"}, {"line": 215, "type": "error", "content": "console.error('MySQL database does not exist. Please check database name.');", "fullLine": "    console.error('MySQL database does not exist. Please check database name.');\r"}, {"line": 217, "type": "error", "content": "console.error('MySQL connection lost. Attempting to recreate pool...');", "fullLine": "    console.error('MySQL connection lost. Attempting to recreate pool...');\r"}, {"line": 221, "type": "error", "content": "console.error('Failed to recreate MySQL pool:', reconnectError);", "fullLine": "      console.error('Failed to recreate MySQL pool:', reconnectError);\r"}, {"line": 224, "type": "error", "content": "console.error('MySQL connection timeout. The server may be overloaded.');", "fullLine": "    console.error('MySQL connection timeout. The server may be overloaded.');\r"}, {"line": 226, "type": "error", "content": "console.error('Lock wait timeout exceeded. This may indicate database contention.');", "fullLine": "    console.error('Lock wait timeout exceeded. This may indicate database contention.');\r"}, {"line": 267, "type": "error", "content": "console.error('Transaction query error:', {", "fullLine": "      console.error('Transaction query error:', {\r"}, {"line": 290, "type": "warn", "content": "console.warn('Attempted to rollback inactive transaction or missing connection');", "fullLine": "      console.warn('Attempted to rollback inactive transaction or missing connection');\r"}, {"line": 298, "type": "error", "content": "console.error('Failed to rollback transaction:', rollbackError);", "fullLine": "      console.error('Failed to rollback transaction:', rollbackError);\r"}, {"line": 340, "type": "error", "content": "console.error('Error during transaction rollback (original error will be thrown):', rollbackError);", "fullLine": "      console.error('Error during transaction rollback (original error will be thrown):', rollbackError);\r"}], "count": 13}, {"path": "src\\lib\\jwt.ts", "statements": [{"line": 35, "type": "error", "content": "console.error('verifyToken should not be called on the client side. Use decodeTokenUnsafe for client-side token parsing.');", "fullLine": "    console.error('verifyToken should not be called on the client side. Use decodeTokenUnsafe for client-side token parsing.');\r"}, {"line": 64, "type": "error", "content": "console.error('JWT verification failed: Invalid token format');", "fullLine": "        console.error('JWT verification failed: Invalid token format');\r"}, {"line": 66, "type": "error", "content": "console.error('JWT token expired');", "fullLine": "        console.error('JWT token expired');\r"}, {"line": 68, "type": "error", "content": "console.error('JWT token not active yet');", "fullLine": "        console.error('JWT token not active yet');\r"}, {"line": 70, "type": "error", "content": "console.error('JWT verification error: Unknown error type');", "fullLine": "        console.error('JWT verification error: Unknown error type');\r"}, {"line": 73, "type": "error", "content": "console.error('JWT verification error: Unexpected error format');", "fullLine": "      console.error('JWT verification error: Unexpected error format');\r"}, {"line": 91, "type": "error", "content": "console.error('SECURITY WARNING: Client-side JWT decoding is disabled. Use server-side API endpoints instead.');", "fullLine": "    console.error('SECURITY WARNING: Client-side JWT decoding is disabled. Use server-side API endpoints instead.');\r"}, {"line": 96, "type": "warn", "content": "console.warn('DEPRECATED: Use verifyToken() for server-side JWT processing');", "fullLine": "  console.warn('DEPRECATED: Use verifyToken() for server-side JWT processing');\r"}], "count": 8}, {"path": "src\\lib\\paymongo.ts", "statements": [{"line": 121, "type": "error", "content": "console.error('PayMongo Source API Error Response:', error);", "fullLine": "    console.error('PayMongo Source API Error Response:', error);\r"}, {"line": 267, "type": "error", "content": "console.error('PayMongo Refund API Error Response:', error);", "fullLine": "    console.error('PayMongo Refund API Error Response:', error);\r"}, {"line": 284, "type": "warn", "content": "console.warn('Webhook secret not configured, skipping signature validation');", "fullLine": "      console.warn('Webhook secret not configured, skipping signature validation');\r"}, {"line": 289, "type": "error", "content": "console.error('No signature provided in webhook');", "fullLine": "      console.error('No signature provided in webhook');\r"}, {"line": 310, "type": "error", "content": "console.error('Invalid signature format');", "fullLine": "      console.error('Invalid signature format');\r"}, {"line": 338, "type": "error", "content": "console.error('Webhook signature validation failed');", "fullLine": "      console.error('Webhook signature validation failed');\r"}, {"line": 343, "type": "error", "content": "console.error('Error validating webhook signature:', error);", "fullLine": "    console.error('Error validating webhook signature:', error);\r"}], "count": 7}, {"path": "src\\lib\\revenueCalculator.ts", "statements": [{"line": 65, "type": "error", "content": "console.error('Error calculating revenue:', error);", "fullLine": "    console.error('Error calculating revenue:', error);\r"}], "count": 1}, {"path": "src\\lib\\smsService.ts", "statements": [{"line": 13, "type": "warn", "content": "console.warn('<PERSON>wi<PERSON> credentials not configured. SMS notifications will be disabled.');", "fullLine": "    console.warn('<PERSON>wi<PERSON> credentials not configured. SMS notifications will be disabled.');\r"}, {"line": 21, "type": "error", "content": "console.error('Failed to initialize Twilio client:', error);", "fullLine": "      console.error('Failed to initialize Twilio client:', error);\r"}, {"line": 95, "type": "error", "content": "console.error('Error sending SMS:', error);", "fullLine": "    console.error('Error sending SMS:', error);\r"}], "count": 3}, {"path": "src\\services\\paymentService.ts", "statements": [{"line": 277, "type": "error", "content": "console.error('Error getting payment status:', error);", "fullLine": "    console.error('Error getting payment status:', error);"}, {"line": 298, "type": "error", "content": "console.error('Transaction not found for source:', sourceId);", "fullLine": "      console.error('Transaction not found for source:', sourceId);"}, {"line": 343, "type": "error", "content": "console.error('Error processing payment webhook:', error);", "fullLine": "    console.error('Error processing payment webhook:', error);"}], "count": 3}, {"path": "src\\services\\refundService.ts", "statements": [{"line": 236, "type": "error", "content": "console.error('Error retrieving payment intent:', error);", "fullLine": "        console.error('Error retrieving payment intent:', error);"}, {"line": 269, "type": "error", "content": "console.error('Error searching for payments:', searchError);", "fullLine": "          console.error('Error searching for payments:', searchError);"}, {"line": 300, "type": "error", "content": "console.error('Error retrieving source:', error);", "fullLine": "        console.error('Error retrieving source:', error);"}, {"line": 335, "type": "error", "content": "console.error('Error searching payments by description:', searchError);", "fullLine": "        console.error('Error searching payments by description:', searchError);"}, {"line": 470, "type": "error", "content": "console.error('Error completing refund:', error);", "fullLine": "    console.error('Error completing refund:', error);"}, {"line": 503, "type": "error", "content": "console.error(`Failed to retry refund ${refund.id}:`, error);", "fullLine": "        console.error(`Failed to retry refund ${refund.id}:`, error);"}, {"line": 513, "type": "error", "content": "console.error('Error retrying failed refunds:', error);", "fullLine": "    console.error('Error retrying failed refunds:', error);"}, {"line": 583, "type": "error", "content": "console.error(`Error resolving payment ID for booking ${bookingId}:`, error);", "fullLine": "        console.error(`Error resolving payment ID for booking ${bookingId}:`, error);"}, {"line": 589, "type": "error", "content": "console.error(`Error validating payment data for booking ${bookingId}:`, error);", "fullLine": "    console.error(`Error validating payment data for booking ${bookingId}:`, error);"}], "count": 9}, {"path": "src\\utils\\businessNotificationService.ts", "statements": [{"line": 172, "type": "warn", "content": "console.warn('Error querying email_notifications, falling back to basic query:', queryError);", "fullLine": "      console.warn('Error querying email_notifications, falling back to basic query:', queryError);\r"}, {"line": 186, "type": "warn", "content": "console.warn('Business user not found for email notification');", "fullLine": "      console.warn('Business user not found for email notification');\r"}, {"line": 200, "type": "warn", "content": "console.warn(`No email address found for business user ${userId}`);", "fullLine": "      console.warn(`No email address found for business user ${userId}`);\r"}, {"line": 213, "type": "error", "content": "console.error('Error sending business email notification:', error);", "fullLine": "    console.error('Error sending business email notification:', error);\r"}, {"line": 307, "type": "error", "content": "console.error('Error ensuring notifications table exists:', error);", "fullLine": "    console.error('Error ensuring notifications table exists:', error);\r"}], "count": 5}, {"path": "src\\utils\\comprehensiveNotificationService.ts", "statements": [{"line": 47, "type": "warn", "content": "console.warn('SSE broadcasting not available:', err.message);", "fullLine": "    console.warn('SSE broadcasting not available:', err.message);\r"}, {"line": 200, "type": "error", "content": "console.error('Error creating booking notification:', error);", "fullLine": "    console.error('Error creating booking notification:', error);\r"}, {"line": 288, "type": "error", "content": "console.error('Error creating payment notification:', error);", "fullLine": "    console.error('Error creating payment notification:', error);\r"}, {"line": 337, "type": "error", "content": "console.error('Error creating system notification:', error);", "fullLine": "    console.error('Error creating system notification:', error);\r"}, {"line": 392, "type": "error", "content": "console.error('Error getting booking details:', error);", "fullLine": "    console.error('Error getting booking details:', error);\r"}, {"line": 410, "type": "warn", "content": "console.warn('User email not found for booking notification');", "fullLine": "      console.warn('User email not found for booking notification');\r"}, {"line": 460, "type": "error", "content": "console.error('Error sending booking email notification:', error);", "fullLine": "    console.error('Error sending booking email notification:', error);\r"}, {"line": 523, "type": "error", "content": "console.error('Error sending payment SMS notification:', error);", "fullLine": "    console.error('Error sending payment SMS notification:', error);\r"}, {"line": 583, "type": "error", "content": "console.error('Error sending booking SMS notification:', error);", "fullLine": "    console.error('Error sending booking SMS notification:', error);\r"}, {"line": 665, "type": "error", "content": "console.error('Error creating provider notification:', error);", "fullLine": "    console.error('Error creating provider notification:', error);\r"}, {"line": 714, "type": "error", "content": "console.error('Error scheduling booking reminders:', error);", "fullLine": "    console.error('Error scheduling booking reminders:', error);\r"}, {"line": 745, "type": "error", "content": "console.error('Error scheduling reminder:', error);", "fullLine": "    console.error('Error scheduling reminder:', error);\r"}], "count": 12}, {"path": "src\\utils\\fileSystemUtils.ts", "statements": [{"line": 70, "type": "error", "content": "console.error('Error cleaning up old files:', error);", "fullLine": "    console.error('Error cleaning up old files:', error);\r"}], "count": 1}, {"path": "src\\utils\\imageUtils.ts", "statements": [{"line": 216, "type": "error", "content": "console.error('Failed to update session storage:', error);", "fullLine": "      console.error('Failed to update session storage:', error);\r"}, {"line": 318, "type": "error", "content": "console.error('Profile picture upload error:', error);", "fullLine": "      console.error('Profile picture upload error:', error);\r"}], "count": 2}, {"path": "src\\utils\\logger.ts", "statements": [{"line": 51, "type": "log", "content": "console.log(this.formatMessage('debug', message, ...args));", "fullLine": "      console.log(this.formatMessage('debug', message, ...args));\r"}, {"line": 57, "type": "log", "content": "console.log(this.formatMessage('info', message, ...args));", "fullLine": "      console.log(this.formatMessage('info', message, ...args));\r"}, {"line": 63, "type": "warn", "content": "console.warn(this.formatMessage('warn', message, ...args));", "fullLine": "      console.warn(this.formatMessage('warn', message, ...args));\r"}, {"line": 71, "type": "error", "content": "console.error(this.formatMessage('error', message, ...args));", "fullLine": "        console.error(this.formatMessage('error', message, ...args));\r"}, {"line": 78, "type": "error", "content": "console.error(this.formatMessage('error', message, ...args));", "fullLine": "        console.error(this.formatMessage('error', message, ...args));\r"}, {"line": 85, "type": "error", "content": "console.error(this.formatMessage('error', `[CRITICAL] ${message}`, ...args));", "fullLine": "    console.error(this.formatMessage('error', `[CRITICAL] ${message}`, ...args));\r"}], "count": 6}], "recommendations": [{"priority": "HIGH", "description": "Core library files (lib/, utils/)", "files": []}, {"priority": "MEDIUM", "description": "Hooks and services", "files": []}, {"priority": "MEDIUM", "description": "Components", "files": []}, {"priority": "LOW", "description": "App pages", "files": []}]}