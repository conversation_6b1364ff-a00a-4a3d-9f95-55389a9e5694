const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript/JavaScript files
function findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Function to analyze console statements in a file
function analyzeConsoleStatements(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const consoleStatements = [];
  
  lines.forEach((line, index) => {
    const lineNumber = index + 1;
    const trimmedLine = line.trim();
    
    // Match console.log, console.warn, console.error, console.info, console.debug
    const consoleMatch = trimmedLine.match(/console\.(log|warn|error|info|debug)\s*\(/);
    if (consoleMatch) {
      consoleStatements.push({
        line: lineNumber,
        type: consoleMatch[1],
        content: trimmedLine,
        fullLine: line
      });
    }
  });
  
  return consoleStatements;
}

// Main analysis
const srcDir = path.join(__dirname, 'src');
const files = findFiles(srcDir);

const report = {
  totalFiles: files.length,
  filesWithConsole: 0,
  totalConsoleStatements: 0,
  byType: {
    log: 0,
    warn: 0,
    error: 0,
    info: 0,
    debug: 0
  },
  byFile: [],
  recommendations: []
};

for (const file of files) {
  const relativePath = path.relative(__dirname, file);
  const consoleStatements = analyzeConsoleStatements(file);
  
  if (consoleStatements.length > 0) {
    report.filesWithConsole++;
    report.totalConsoleStatements += consoleStatements.length;
    
    const fileReport = {
      path: relativePath,
      statements: consoleStatements,
      count: consoleStatements.length
    };
    
    // Count by type
    consoleStatements.forEach(stmt => {
      report.byType[stmt.type]++;
    });
    
    report.byFile.push(fileReport);
  }
}

// Generate recommendations
report.recommendations = [
  {
    priority: 'HIGH',
    description: 'Core library files (lib/, utils/)',
    files: report.byFile.filter(f => f.path.startsWith('src/lib/') || f.path.startsWith('src/utils/'))
  },
  {
    priority: 'MEDIUM',
    description: 'Hooks and services',
    files: report.byFile.filter(f => f.path.startsWith('src/hooks/') || f.path.startsWith('src/services/'))
  },
  {
    priority: 'MEDIUM',
    description: 'Components',
    files: report.byFile.filter(f => f.path.startsWith('src/components/'))
  },
  {
    priority: 'LOW',
    description: 'App pages',
    files: report.byFile.filter(f => f.path.startsWith('src/app/'))
  }
];

// Output the report
console.log('=== CONSOLE STATEMENT CLEANUP REPORT ===');
console.log(`Total files scanned: ${report.totalFiles}`);
console.log(`Files with console statements: ${report.filesWithConsole}`);
console.log(`Total console statements: ${report.totalConsoleStatements}`);
console.log('\n=== BY TYPE ===');
Object.entries(report.byType).forEach(([type, count]) => {
  console.log(`${type}: ${count}`);
});

console.log('\n=== FILES WITH CONSOLE STATEMENTS ===');
report.byFile.forEach(file => {
  console.log(`${file.path}: ${file.count} statements`);
  file.statements.forEach(stmt => {
    console.log(`  Line ${stmt.line}: console.${stmt.type}`);
  });
});

console.log('\n=== RECOMMENDED CLEANUP ORDER ===');
report.recommendations.forEach(rec => {
  console.log(`\n${rec.priority} PRIORITY: ${rec.description}`);
  rec.files.forEach(file => {
    console.log(`  - ${file.path} (${file.count} statements)`);
  });
});

// Save detailed report to file
fs.writeFileSync('console_cleanup_detailed.json', JSON.stringify(report, null, 2));
console.log('\nDetailed report saved to console_cleanup_detailed.json'); 